-- Create the schedule_post table for storing scheduled social media posts
-- Run this SQL in your Supabase SQL Editor to create the missing table

CREATE TABLE IF NOT EXISTS public.schedule_post (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    social_network_id TEXT NOT NULL,
    content_types TEXT[] DEFAULT '{}',
    platform TEXT NOT NULL DEFAULT 'x',
    scheduled_at TIMESTAMPTZ NOT NULL,
    title TEXT,
    content TEXT,
    num_occurrences TEXT DEFAULT '1',
    frequency TEXT DEFAULT 'once',
    date_created TIMESTAMPTZ DEFAULT NOW(),
    date_updated TIMESTAMPTZ DEFAULT NOW(),
    
    -- Additional fields that might be useful
    status TEXT DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'published', 'failed', 'cancelled')),
    user_id UUID, -- Add if you have user authentication
    
    -- Indexes for better performance
    CONSTRAINT schedule_post_platform_check CHECK (platform IN ('x', 'instagram', 'facebook', 'linkedin', 'tiktok'))
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_schedule_post_scheduled_at ON public.schedule_post(scheduled_at);
CREATE INDEX IF NOT EXISTS idx_schedule_post_platform ON public.schedule_post(platform);
CREATE INDEX IF NOT EXISTS idx_schedule_post_status ON public.schedule_post(status);

-- Create a trigger to automatically update the date_updated field
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.date_updated = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_schedule_post_updated_at 
    BEFORE UPDATE ON public.schedule_post 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create the post table if it doesn't exist (for the foreign key relationship)
CREATE TABLE IF NOT EXISTS public.post (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    content TEXT NOT NULL,
    media_url TEXT,
    hashtags TEXT[] DEFAULT '{}',
    mentions TEXT[] DEFAULT '{}',
    date_created TIMESTAMPTZ DEFAULT NOW(),
    date_updated TIMESTAMPTZ DEFAULT NOW()
);

-- Add foreign key relationship (optional, but recommended)
-- ALTER TABLE public.schedule_post ADD COLUMN post_id UUID REFERENCES public.post(id);

-- Enable Row Level Security (RLS) if you have user authentication
-- ALTER TABLE public.schedule_post ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE public.post ENABLE ROW LEVEL SECURITY;

-- Create policies for RLS (uncomment and modify if you have user authentication)
-- CREATE POLICY "Users can view their own scheduled posts" ON public.schedule_post
--     FOR SELECT USING (auth.uid() = user_id);

-- CREATE POLICY "Users can insert their own scheduled posts" ON public.schedule_post
--     FOR INSERT WITH CHECK (auth.uid() = user_id);

-- CREATE POLICY "Users can update their own scheduled posts" ON public.schedule_post
--     FOR UPDATE USING (auth.uid() = user_id);

-- CREATE POLICY "Users can delete their own scheduled posts" ON public.schedule_post
--     FOR DELETE USING (auth.uid() = user_id);

COMMENT ON TABLE public.schedule_post IS 'Stores scheduled social media posts with timing and platform information';
COMMENT ON COLUMN public.schedule_post.scheduled_at IS 'When the post should be published';
COMMENT ON COLUMN public.schedule_post.content_types IS 'Array of content types like [''text'', ''image'', ''video'']';
COMMENT ON COLUMN public.schedule_post.platform IS 'Social media platform (x, instagram, facebook, linkedin, tiktok)';
COMMENT ON COLUMN public.schedule_post.frequency IS 'How often to repeat (once, daily, weekly, monthly)';
COMMENT ON COLUMN public.schedule_post.num_occurrences IS 'Number of times to repeat the post';
