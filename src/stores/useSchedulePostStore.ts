import { create } from "zustand";
import { supabase } from "../services/supabase";
import { PostSchedule } from "../types";

interface SchedulePostStore {
  scheduledPosts: PostSchedule[];
  fetchScheduledPostById: (id: string) => Promise<PostSchedule | null>;
  fetchScheduledPosts: () => Promise<void>;
  upsertScheduledPost: (
    data: Partial<PostSchedule> & { id?: string },
  ) => Promise<PostSchedule | null>;
  deleteScheduledPost: (id: string) => Promise<void>;
  timeInterval: number;
  setTimeInterval: (minutes: number) => void;
}

export const useSchedulePostStore = create<SchedulePostStore>((set) => ({
  scheduledPosts: [],
  timeInterval: 60,
  setTimeInterval: (minutes: number) =>
    set(() => ({
      timeInterval: minutes,
    })),

  fetchScheduledPostById: async (id: string) => {
    const { data, error } = await supabase
      .from("schedule_post")
      .select("*, post(*)")
      .eq("id", id)
      .single();

    if (error) {
      console.error("Error fetching schedule posts:", error?.message);
      return null;
    }

    return data as unknown as PostSchedule;
  },

  fetchScheduledPosts: async () => {
    const { data, error } = await supabase
      .from("schedule_post")
      .select("*")
      .order("scheduled_at", { ascending: true });

    if (!error && data) {
      set({ scheduledPosts: data as unknown as PostSchedule[] });
    } else {
      console.error("Error fetching schedule posts:", error?.message);
      // If table doesn't exist, use localStorage fallback
      if (error?.message?.includes("does not exist")) {
        console.warn("Schedule post table doesn't exist. Using local storage fallback.");
        const localPosts = localStorage.getItem('scheduledPosts');
        if (localPosts) {
          try {
            const parsedPosts = JSON.parse(localPosts);
            set({ scheduledPosts: parsedPosts });
          } catch (e) {
            console.error("Error parsing local posts:", e);
          }
        }
      }
    }
  },

  upsertScheduledPost: async (scheduleData) => {
    // Try database first
    if (scheduleData.id) {
      const { data, error } = await supabase
        .from("schedule_post")
        .update(scheduleData)
        .eq("id", scheduleData.id)
        .select()
        .single();

      if (error) {
        console.error("Error updating schedule post:", error.message);
        // Fallback to localStorage
        if (error.message.includes("does not exist")) {
          return handleLocalStorageFallback(scheduleData, true);
        }
        return null;
      }

      const typedSchedulePost = data as unknown as PostSchedule;

      set((state) => ({
        scheduledPosts: state.scheduledPosts.map((s) =>
          s.id === scheduleData.id ? typedSchedulePost : s,
        ),
      }));

      return typedSchedulePost;
    } else {
      const { data, error } = await supabase
        .from("schedule_post")
        .insert(scheduleData)
        .select()
        .single();

      if (error) {
        console.error("Error creating schedule post:", error.message);
        // Fallback to localStorage
        if (error.message.includes("does not exist")) {
          return handleLocalStorageFallback(scheduleData, false);
        }
        return null;
      }

      const typedSchedulePost = data as unknown as PostSchedule;

      set((state) => ({
        scheduledPosts: [...state.scheduledPosts, typedSchedulePost],
      }));

      return typedSchedulePost;
    }

    // Local storage fallback function
    function handleLocalStorageFallback(data: any, isUpdate: boolean) {
      const newPost = {
        ...data,
        id: data.id || Math.random().toString(36).substr(2, 9),
      } as PostSchedule;

      set((state) => {
        let newPosts;
        if (isUpdate) {
          newPosts = state.scheduledPosts.map((s) =>
            s.id === data.id ? newPost : s,
          );
        } else {
          newPosts = [...state.scheduledPosts, newPost];
        }

        // Save to localStorage
        localStorage.setItem('scheduledPosts', JSON.stringify(newPosts));

        return { scheduledPosts: newPosts };
      });

      return newPost;
    }
  },

  deleteScheduledPost: async (id) => {
    const { error } = await supabase
      .from("schedule_post")
      .delete()
      .eq("id", id);

    if (error) {
      console.error("Error deleting schedule post:", error.message);
      // If table doesn't exist, still remove from local state
      if (error.message.includes("does not exist")) {
        console.warn("Using localStorage fallback for delete");
      } else {
        return;
      }
    }

    set((state) => {
      const newPosts = state.scheduledPosts.filter((s) => s.id !== id);
      // Update localStorage
      localStorage.setItem('scheduledPosts', JSON.stringify(newPosts));
      return { scheduledPosts: newPosts };
    });
  },
}));

// Platform-specific colors
export const platformColors: Record<
  any,
  {
    bg: string;
    text: string;
    hover: string;
    border: string;
  }
> = {
  x: {
    bg: "bg-neutral-100 dark:bg-neutral-900/50",
    text: "text-neutral-600 dark:text-neutral-400",
    hover: "hover:ring-neutral-400/50",
    border: "border-neutral-400",
  },
  instagram: {
    bg: "bg-fuchsia-100 dark:bg-fuchsia-900/50",
    text: "text-fuchsia-600 dark:text-fuchsia-400",
    hover: "hover:ring-fuchsia-400/50",
    border: "border-fuchsia-400",
  },
  facebook: {
    bg: "bg-blue-100 dark:bg-blue-900/50",
    text: "text-blue-600 dark:text-blue-400",
    hover: "hover:ring-blue-400/50",
    border: "border-blue-400",
  },
  linkedin: {
    bg: "bg-sky-100 dark:bg-sky-900/50",
    text: "text-sky-600 dark:text-sky-400",
    hover: "hover:ring-sky-400/50",
    border: "border-sky-400",
  },
};
