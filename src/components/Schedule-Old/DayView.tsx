import React, { useState, useCallback, useMemo, useEffect } from "react";
import {
  Clock,
  Plus,
  Trash2,
  MessageSquare,
  Image,
  Video,
  Music,
  AlertCircle,
  X,
} from "lucide-react";
import { EventModal } from "./EventModal";
import {
  useSchedulePostStore,
  platformColors,
} from "../../stores/useSchedulePostStore.ts";
import { XIcon } from "../icons/XIcon";
import { Instagram, Facebook, Linkedin } from "lucide-react";
import toast from "react-hot-toast";

// Platform icons mapping
const platformIcons = {
  x: XIcon,
  instagram: Instagram,
  facebook: Facebook,
  linkedin: Linkedin,
};

// Content type icons
const contentTypeIcons = {
  text: MessageSquare,
  image: Image,
  video: Video,
  audio: Music,
};

// Static Event Component (replaces DraggableEvent)
function StaticEvent({
  id,
  title,
  time,
  platform = "x",
  contentTypes = [],
  onClick,
  onDelete,
}: {
  id: string;
  title: string;
  time: string;
  platform?: string;
  contentTypes?: string[];
  onClick?: () => void;
  onDelete?: () => void;
}) {
  const color = platformColors[platform] || platformColors.default;
  const PlatformIcon = platformIcons[platform as keyof typeof platformIcons] || XIcon;

  return (
    <div
      className={`relative p-2 mb-1 rounded-lg border-l-4 cursor-pointer transition-all hover:shadow-md group ${color.bg} ${color.border} ${color.text}`}
      onClick={onClick}
      role="button"
      tabIndex={0}
      onKeyPress={(e) => e.key === "Enter" && onClick?.()}
      aria-label={`Event: ${title} at ${time}`}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-1 mb-1">
            <PlatformIcon className="w-3 h-3 flex-shrink-0" />
            <span className="text-xs font-medium truncate">{title}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Clock className="w-3 h-3 flex-shrink-0" />
            <span className="text-xs">{time}</span>
          </div>
          {contentTypes.length > 0 && (
            <div className="flex items-center space-x-1 mt-1">
              {contentTypes.slice(0, 2).map((type) => {
                const Icon = contentTypeIcons[type as keyof typeof contentTypeIcons] || MessageSquare;
                return <Icon key={type} className="w-3 h-3" />;
              })}
              {contentTypes.length > 2 && (
                <span className="text-xs">+{contentTypes.length - 2}</span>
              )}
            </div>
          )}
        </div>
        {onDelete && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
            className="opacity-0 group-hover:opacity-100 p-1 hover:bg-red-100 dark:hover:bg-red-900/30 rounded transition-all"
            aria-label="Delete event"
          >
            <X className="w-3 h-3 text-red-600 dark:text-red-400" />
          </button>
        )}
      </div>
    </div>
  );
}



export function DayView({ currentDate }: { currentDate: Date }) {
  // Fix mapping from store
  const {
    scheduledPosts = [],
    fetchScheduledPosts,
    upsertScheduledPost,
    deleteScheduledPost,
    timeInterval = 30,
  } = useSchedulePostStore() || {};

  // Map scheduledPosts to events format
  const events = useMemo(() => {
    return Array.isArray(scheduledPosts)
      ? scheduledPosts.map((post) => ({
          id: post.id,
          title: post.title || `Post - ${post.platform || "unknown"}`,
          date: new Date(post.scheduled_at),
          time: new Date(post.scheduled_at).toLocaleTimeString([], {
            hour: "numeric",
            minute: "2-digit",
            hour12: true,
          }),
          platform: post.platform || "unknown",
          contentTypes: post.content_types || ["text"],
          content: post.content,
        }))
      : [];
  }, [scheduledPosts]);

  // Load posts when component mounts
  useEffect(() => {
    if (fetchScheduledPosts) {
      console.log("[DayView] Fetching scheduled posts...");
      fetchScheduledPosts().catch((err) =>
        console.error("[DayView] Error fetching scheduled posts:", err)
      );
    }
  }, [fetchScheduledPosts]);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedEventId, setSelectedEventId] = useState<string | null>(null);
  const [selectedTime, setSelectedTime] = useState<{
    hour: number;
    minute: number;
  } | null>(null);
  const [error, setError] = useState<string | null>(null);

  const getTimeSlots = useMemo(() => {
    const slots = [];
    const totalMinutes = 24 * 60;
    for (let minutes = 0; minutes < totalMinutes; minutes += timeInterval) {
      const hour = Math.floor(minutes / 60);
      const minute = minutes % 60;
      slots.push({ hour, minute });
    }
    return slots;
  }, [timeInterval]);

  const formatTime = (hour: number, minute: number = 0) => {
    const timeString = new Date(2000, 0, 1, hour, minute).toLocaleTimeString(
      undefined,
      {
        hour: "numeric",
        minute: "2-digit",
        hour12: true,
      },
    );

    // Add a space between time and AM/PM for better readability
    return timeString.replace(/([AP])M/, " $1M");
  };

  const getEventsForTimeSlot = useCallback(
    (hour: number, minute: number) => {
      // Skip if no events available
      if (!Array.isArray(events) || events.length === 0) {
        return [];
      }
      
      try {
        // Create date objects for comparison
        const targetDate = new Date(currentDate);
        targetDate.setHours(hour, minute, 0, 0);
        
        const slotStart = new Date(targetDate);
        const slotEnd = new Date(targetDate);
        slotEnd.setMinutes(slotEnd.getMinutes() + timeInterval - 1);
        
        return events.filter((event) => {
          try {
            // Make sure event.date is a Date object
            const eventDate = event.date instanceof Date ? event.date : new Date(event.date);
            
            // Check if event falls within time slot
            return (
              eventDate >= slotStart && eventDate <= slotEnd
            );
          } catch (err) {
            console.error("[DayView] Error filtering event:", err);
            return false;
          }
        });
      } catch (err) {
        console.error("[DayView] Error in getEventsForTimeSlot:", err);
        return [];
      }
    },
    [events, timeInterval, currentDate],
  );

  const handleAddEvent = useCallback(
    (e: React.MouseEvent, hour: number, minute: number) => {
      e.preventDefault();
      e.stopPropagation();
      setSelectedEventId(null);
      setSelectedTime({ hour, minute });
      setIsModalOpen(true);
    },
    [],
  );

  const handleEventClick = useCallback(
    (eventId: string) => {
      setSelectedEventId(eventId);
      setSelectedTime(null);
      setIsModalOpen(true);
    },
    [],
  );

  const handleModalSave = useCallback(
    (data: { types: string[]; platform: string; content?: any }) => {
      try {
        const typeLabels = data.types
          .map((type) => type.charAt(0).toUpperCase() + type.slice(1))
          .join(" + ");

        if (selectedEventId) {
          // Update existing event
          upsertScheduledPost(selectedEventId, {
            title: `${typeLabels} - ${data.platform}`,
            content_types: data.types,
            platform: data.platform,
            content: data.content,
          });
          toast.success("Event updated successfully");
        } else if (selectedTime) {
          // Create new event
          const date = new Date(currentDate);
          date.setHours(selectedTime.hour, selectedTime.minute);

          const eventId = crypto.randomUUID();
          upsertScheduledPost(eventId, {
            id: eventId,
            title: `${typeLabels} - ${data.platform}`,
            scheduled_at: date,
            time: formatTime(selectedTime.hour, selectedTime.minute),
            content_types: data.types,
            platform: data.platform,
            content: data.content,
          });
          toast.success("Event created successfully");
        }

        setError(null);
      } catch (err) {
        setError("Failed to save event");
        toast.error("Error saving event");
        console.error("Save error:", err);
      }

      setSelectedEventId(null);
      setSelectedTime(null);
      setIsModalOpen(false);
    },
    [
      selectedEventId,
      selectedTime,
      currentDate,
      upsertScheduledPost,
      formatTime,
    ],
  );

  const handleDeleteEvent = useCallback(
    (eventId: string) => {
      try {
        deleteScheduledPost(eventId);
        toast.success("Event deleted successfully");
        setError(null);
      } catch (err) {
        setError("Failed to delete event");
        toast.error("Error deleting event");
        console.error("Delete error:", err);
      }
    },
    [deleteScheduledPost],
  );

  const selectedEvent = useMemo(
    () =>
      selectedEventId
        ? events.find((event) => event.id === selectedEventId)
        : undefined,
    [selectedEventId, events],
  );

  return (
    <div className="h-[calc(100vh-15rem)] sm:h-[calc(100vh-16rem)] w-full flex flex-col overflow-hidden">
        {error && (
          <div className="p-3 mb-3 bg-red-50 dark:bg-red-900/30 rounded-lg flex items-center space-x-2">
            <AlertCircle className="w-4 h-4 text-red-600 dark:text-red-400" />
            <span className="text-xs sm:text-sm text-red-600 dark:text-red-400">
              {error}
            </span>
          </div>
        )}

        <div className="flex-1 overflow-y-auto w-full">
          <div className="min-w-full w-full">
            <div
              className="space-y-1"
              role="grid"
              aria-label={`Schedule for ${currentDate.toDateString()}`}
            >
              {getTimeSlots.map(({ hour, minute }) => {
                const timeSlotEvents = getEventsForTimeSlot(hour, minute);
                return (
                  <div
                    key={`${hour}:${minute}`}
                    className="grid grid-cols-[90px_1fr] group hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
                  >
                    <div className="text-xs sm:text-sm md:text-base text-gray-500 dark:text-gray-300 py-3 sm:py-6 pr-3 text-right sticky left-0 bg-white dark:bg-gray-800 z-10 font-medium">
                      {formatTime(hour, minute)}
                    </div>
                    <div className="h-full group relative">
                      <div className="relative min-h-[80px]">
                        {timeSlotEvents.length === 0 && (
                          <button
                            className="absolute inset-0 w-full h-full opacity-0 group-hover:opacity-100 flex items-center justify-center bg-indigo-50/50 dark:bg-indigo-900/20 transition-opacity focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                            onClick={(e) => handleAddEvent(e, hour, minute)}
                            aria-label={`Add event at ${formatTime(hour, minute)}`}
                          >
                            <Plus className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
                          </button>
                        )}
                        <div className="border-l border-gray-200 dark:border-gray-700 p-2 h-full">
                          {timeSlotEvents.map((event) => (
                            <StaticEvent
                              key={event.id}
                              id={event.id}
                              title={event.title}
                              time={event.time}
                              platform={event.platform}
                              contentTypes={event.contentTypes}
                              onClick={() => handleEventClick(event.id)}
                              onDelete={() => handleDeleteEvent(event.id)}
                            />
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        <EventModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setSelectedEventId(null);
            setSelectedTime(null);
            setError(null);
          }}
          onSave={handleModalSave}
          onDelete={
            selectedEventId ? () => handleDeleteEvent(selectedEventId) : undefined
          }
          isExisting={!!selectedEventId}
          event={selectedEvent}
        />
      </div>
  );
}
