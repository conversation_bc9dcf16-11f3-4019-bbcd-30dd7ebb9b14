import React, { useState, useCallback, useMemo, useEffect } from "react";
import { Clock, Plus, AlertCircle, MessageSquare, Image, Video, Music, X } from "lucide-react";
import { EventModal } from "./EventModal";
import { useSchedulePostStore, platformColors } from "../../stores/useSchedulePostStore.ts";
import { XIcon } from "../icons/XIcon";
import { Instagram, Facebook, Linkedin } from "lucide-react";
import toast from "react-hot-toast";

interface WeekViewProps {
  currentDate: Date;
  onDayClick: (date: Date) => void;
}

const DAYS = ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Wed", "Thu", "Fri", "Sat"];

// Platform icons mapping
const platformIcons = {
  x: XIcon,
  instagram: Instagram,
  facebook: Facebook,
  linkedin: Linkedin,
};

// Content type icons
const contentTypeIcons = {
  text: MessageSquare,
  image: Image,
  video: Video,
  audio: Music,
};

// Static Event Component (replaces DraggableEvent)
function StaticEvent({
  id,
  title,
  time,
  platform = "x",
  contentTypes = [],
  onClick,
  onDelete,
}: {
  id: string;
  title: string;
  time: string;
  platform?: string;
  contentTypes?: string[];
  onClick?: () => void;
  onDelete?: () => void;
}) {
  const color = platformColors[platform] || platformColors.default;
  const PlatformIcon = platformIcons[platform as keyof typeof platformIcons] || XIcon;

  return (
    <div
      className={`relative p-2 mb-1 rounded-lg border-l-4 cursor-pointer transition-all hover:shadow-md group ${color.bg} ${color.border} ${color.text}`}
      onClick={onClick}
      role="button"
      tabIndex={0}
      onKeyPress={(e) => e.key === "Enter" && onClick?.()}
      aria-label={`Event: ${title} at ${time}`}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-1 mb-1">
            <PlatformIcon className="w-3 h-3 flex-shrink-0" />
            <span className="text-xs font-medium truncate">{title}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Clock className="w-3 h-3 flex-shrink-0" />
            <span className="text-xs">{time}</span>
          </div>
          {contentTypes.length > 0 && (
            <div className="flex items-center space-x-1 mt-1">
              {contentTypes.slice(0, 2).map((type) => {
                const Icon = contentTypeIcons[type as keyof typeof contentTypeIcons] || MessageSquare;
                return <Icon key={type} className="w-3 h-3" />;
              })}
              {contentTypes.length > 2 && (
                <span className="text-xs">+{contentTypes.length - 2}</span>
              )}
            </div>
          )}
        </div>
        {onDelete && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
            className="opacity-0 group-hover:opacity-100 p-1 hover:bg-red-100 dark:hover:bg-red-900/30 rounded transition-all"
            aria-label="Delete event"
          >
            <X className="w-3 h-3 text-red-600 dark:text-red-400" />
          </button>
        )}
      </div>
    </div>
  );
}



export function WeekView({ currentDate, onDayClick }: WeekViewProps) {
  const {
    scheduledPosts = [],
    fetchScheduledPosts,
    upsertScheduledPost,
    deleteScheduledPost,
    timeInterval = 30,
  } = useSchedulePostStore() || {};

  const events = useMemo(() => {
    return Array.isArray(scheduledPosts)
      ? scheduledPosts.map((post) => ({
          id: post.id,
          title: post.title || `Post - ${post.platform || "unknown"}`,
          date: new Date(post.scheduled_at),
          time: new Date(post.scheduled_at).toLocaleTimeString([], {
            hour: "numeric",
            minute: "2-digit",
            hour12: true,
          }),
          platform: post.platform || "unknown",
          contentTypes: post.content_types || ["text"],
          content: post.content,
        }))
      : [];
  }, [scheduledPosts]);

  console.log(
    "[WeekView] Store loaded:",
    {
      hasScheduledPosts: Array.isArray(scheduledPosts),
      postsCount: Array.isArray(scheduledPosts) ? scheduledPosts.length : 0,
      eventsCount: events.length,
      timeInterval,
    }
  );

  useEffect(() => {
    if (fetchScheduledPosts) {
      console.log("[WeekView] Fetching scheduled posts...");
      fetchScheduledPosts().catch((err) =>
        console.error("[WeekView] Error fetching scheduled posts:", err)
      );
    }
  }, [fetchScheduledPosts]);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedEventId, setSelectedEventId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const weekDates = useMemo(() => {
    const dates = [];
    const weekStart = new Date(currentDate);
    weekStart.setDate(currentDate.getDate() - currentDate.getDay());

    for (let i = 0; i < 7; i++) {
      const date = new Date(weekStart);
      date.setDate(weekStart.getDate() + i);
      dates.push(date);
    }
    return dates;
  }, [currentDate]);

  const timeSlots = useMemo(() => {
    const slots = [];
    const totalMinutes = 24 * 60;
    for (let minutes = 0; minutes < totalMinutes; minutes += timeInterval) {
      const hour = Math.floor(minutes / 60);
      const minute = minutes % 60;
      slots.push({ hour, minute });
    }
    return slots;
  }, [timeInterval]);

  const formatTime = (hour: number, minute: number = 0) => {
    const timeString = new Date(2000, 0, 1, hour, minute).toLocaleTimeString(
      undefined,
      {
        hour: "numeric",
        minute: "2-digit",
        hour12: true,
      }
    );

    return timeString.replace(/([AP])M/, " $1M");
  };



  const getEventsForTimeSlot = useCallback(
    (hour: number, minute: number, dayIndex: number) => {
      // Make sure to properly set the date for comparison
      const currentDate = new Date(weekDates[dayIndex]);
      currentDate.setHours(hour, minute, 0, 0);
      
      // Get the start and end of the time slot
      const slotStart = new Date(currentDate);
      const slotEnd = new Date(currentDate);
      slotEnd.setMinutes(slotEnd.getMinutes() + timeInterval - 1);
      
      return events.filter((event) => {
        try {
          // Make sure event.date is a Date object
          const eventDate = event.date instanceof Date ? event.date : new Date(event.date);
          
          // Check if eventDate falls within the time slot
          return (
            eventDate >= slotStart && eventDate <= slotEnd
          );
        } catch (err) {
          console.error("[WeekView] Error filtering events:", err);
          return false;
        }
      });
    },
    [events, timeInterval, weekDates],
  );

  const handleAddEvent = useCallback(
    (e: React.MouseEvent, hour: number, minute: number, dayIndex: number) => {
      e.preventDefault();
      e.stopPropagation();

      try {
        const date = new Date(weekDates[dayIndex]);
        date.setHours(hour, minute);

        if (upsertScheduledPost) {
          const newEvent = {
            id: crypto.randomUUID(),
            title: "New Event",
            date,
            time: formatTime(hour, minute),
          };

          upsertScheduledPost({
            title: newEvent.title,
            platform: "x",
            content_types: ["text"],
            content: {},
            scheduled_at: newEvent.date.toISOString(),
          });
          setSelectedEventId(newEvent.id);
          setIsModalOpen(true);
          setError(null);
          toast.success("Event created");
        }
      } catch (err) {
        setError("Failed to create event");
        toast.error("Error creating event");
        console.error("Add event error:", err);
      }
    },
    [upsertScheduledPost, formatTime, weekDates]
  );

  const handleEventClick = useCallback(
    (eventId: string) => {
      setSelectedEventId(eventId);
      setIsModalOpen(true);
    },
    []
  );

  const handleDeleteEvent = useCallback(
    (eventId: string) => {
      try {
        if (deleteScheduledPost) {
          deleteScheduledPost(eventId);
          setError(null);
        }
      } catch (err) {
        setError("Failed to delete event");
        console.error("Delete error:", err);
      }
    },
    [deleteScheduledPost]
  );

  const handleModalSave = useCallback(
    (data: { types: string[]; platform: string; content?: any }) => {
      if (!selectedEventId) return;
      try {
        const typeLabels = data.types
          .map((type) => type.charAt(0).toUpperCase() + type.slice(1))
          .join(" + ");
        if (upsertScheduledPost) {
          upsertScheduledPost({
            id: selectedEventId,
            title: `${typeLabels} - ${data.platform}`,
            platform: data.platform,
            content_types: data.types,
            content: data.content,
          });
          toast.success("Event updated");
          setError(null);
        }
      } catch (err) {
        setError("Failed to save event");
        toast.error("Error saving event");
        console.error("Save error:", err);
      }
    },
    [selectedEventId, upsertScheduledPost]
  );

  const selectedEvent = useMemo(
    () =>
      selectedEventId
        ? events.find((event) => event.id === selectedEventId)
        : undefined,
    [selectedEventId, events]
  );

  return (
    <div className="overflow-x-auto -mx-4 md:mx-0 touch-pan-x">
        <div className="min-w-[800px] px-4 md:px-0">
          {error && (
            <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/30 rounded-lg flex items-center space-x-2">
              <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
              <span className="text-sm text-red-600 dark:text-red-400">
                {error}
              </span>
            </div>
          )}

          {/* Header */}
          <div className="grid grid-cols-[100px_repeat(7,1fr)] mb-4 sticky top-0 bg-white dark:bg-gray-800 z-10 rounded-t-xl">
            <div className="text-sm font-medium text-gray-500 dark:text-gray-400" />
            {weekDates.map((date, index) => {
              const isToday = date.toDateString() === new Date().toDateString();
              return (
                <button
                  key={index}
                  onClick={() => onDayClick(date)}
                  className={`text-sm font-medium text-center transition-colors group ${isToday ? "text-indigo-600 dark:text-indigo-400" : "text-gray-900 dark:text-white hover:text-indigo-600 dark:hover:text-indigo-400"}`}
                  aria-label={`${DAYS[date.getDay()]} ${date.getDate()}`}
                  tabIndex={0}
                  onKeyPress={(e) => e.key === "Enter" && onDayClick(date)}
                >
                  <div>{DAYS[date.getDay()]}</div>
                  <div
                    className={`group-hover:text-indigo-600 dark:group-hover:text-indigo-400 ${isToday ? "text-indigo-600 dark:text-indigo-400" : "text-gray-500 dark:text-gray-400"}`}
                  >
                    {date.getDate()}
                  </div>
                </button>
              );
            })}
          </div>

          {/* Time Slots */}
          <div
            className="space-y-1"
            role="grid"
            aria-label={`Weekly schedule starting ${weekDates[0].toDateString()}`}
          >
            {timeSlots.map(({ hour, minute }) => (
              <div
                key={`${hour}:${minute}`}
                className="grid grid-cols-[120px_repeat(7,1fr)] group hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
              >
                <div className="text-sm md:text-base text-gray-500 dark:text-gray-300 py-5 pr-5 text-right sticky left-0 bg-white dark:bg-gray-800 z-10 font-medium">
                  {formatTime(hour, minute)}
                </div>
                {Array.from({ length: 7 }).map((_, dayIndex) => {
                  const timeSlotEvents = getEventsForTimeSlot(
                    hour,
                    minute,
                    dayIndex
                  );
                  const date = weekDates[dayIndex];

                  const hasEvents = Array.isArray(timeSlotEvents) && timeSlotEvents.length > 0;

                  return (
                    <div
                      key={dayIndex}
                      className="h-full group relative"
                      role="gridcell"
                      aria-label={`Time slot at ${date.toLocaleTimeString()}`}
                    >
                      <div className="relative min-h-[80px]">
                        {!hasEvents && (
                          <button
                            className="absolute inset-0 w-full h-full opacity-0 group-hover:opacity-100 flex items-center justify-center bg-indigo-50/50 dark:bg-indigo-900/20 transition-opacity focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                            onClick={(e) => handleAddEvent(e, hour, minute, dayIndex)}
                            aria-label={`Add event at ${date.toLocaleTimeString()}`}
                          >
                            <Plus className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
                          </button>
                        )}
                        <div className="border-l border-gray-200 dark:border-gray-700 p-2">
                          {Array.isArray(timeSlotEvents) && timeSlotEvents.map((event) => (
                            <StaticEvent
                              key={event.id}
                              id={event.id}
                              title={event.title}
                              time={event.time}
                              platform={event.platform}
                              contentTypes={event.contentTypes}
                              onClick={() => handleEventClick(event.id)}
                              onDelete={() => handleDeleteEvent(event.id)}
                            />
                          ))}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            ))}
          </div>
        </div>

        <EventModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setSelectedEventId(null);
            setError(null);
          }}
          onSave={handleModalSave}
          onDelete={
            selectedEventId ? () => handleDeleteEvent(selectedEventId) : undefined
          }
          isExisting={!!selectedEventId}
          event={selectedEvent}
        />
      </div>
  );
}
