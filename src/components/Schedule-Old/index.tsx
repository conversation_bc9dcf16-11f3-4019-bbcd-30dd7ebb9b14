import React, {
  useState,
  useRef,
  useEffect,
  useCallback,
  useMemo,
} from "react";
import {
  Calendar as CalendarIcon,
  Plus,
  Globe2,
  AlertCircle,
} from "lucide-react";
import { DayView } from "./DayView";
import { WeekView } from "./WeekView";
import { MonthView } from "./MonthView";
import { TimeIntervalSelector } from "./TimeIntervalSelector";
import { useSchedulePostStore } from "../../stores/useSchedulePostStore.ts";
import { BulkCreateModal } from "./BulkCreateModal";
import { BulkCalendarModal } from "./BulkCalendarModal";
import { PageLayout } from "../common/PageLayout";
import toast from "react-hot-toast";

// Add debugging flag to help troubleshoot rendering issues
const DEBUG_MODE = true;

type ViewType = "day" | "week" | "month";

// Sample bulk generated posts for the calendar modal
const sampleBulkPosts = [
  {
    id: "post-1",
    content: "🚀 Excited to share our latest product update! The new features are going to revolutionize how you work. #ProductUpdate #Innovation #TechNews",
    isSelected: false,
    timestamp: new Date("2024-01-15T10:30:00"),
    characterInfo: {
      id: "char-1",
      name: "Tech Enthusiast",
      avatar: "/images/tech-avatar.png"
    },
    platform: "x",
    contentType: "text",
    hashtags: ["ProductUpdate", "Innovation", "TechNews"],
    mentions: []
  },
  {
    id: "post-2",
    content: "Monday motivation: Every expert was once a beginner. Keep pushing forward! 💪 #MondayMotivation #Growth #Success",
    isSelected: false,
    timestamp: new Date("2024-01-15T09:15:00"),
    platform: "instagram",
    contentType: "image",
    hashtags: ["MondayMotivation", "Growth", "Success"],
    mentions: []
  },
  {
    id: "post-3",
    content: "Behind the scenes of our creative process. From concept to execution, every detail matters. ✨ #BehindTheScenes #Creative #Process",
    isSelected: false,
    timestamp: new Date("2024-01-15T14:20:00"),
    characterInfo: {
      id: "char-2",
      name: "Creative Director",
      avatar: "/images/creative-avatar.png"
    },
    platform: "linkedin",
    contentType: "mixed",
    hashtags: ["BehindTheScenes", "Creative", "Process"],
    mentions: []
  },
  {
    id: "post-4",
    content: "Coffee thoughts: The best ideas often come when you least expect them. ☕️ What's your creative ritual? #Coffee #Ideas #Creativity",
    isSelected: false,
    timestamp: new Date("2024-01-15T08:45:00"),
    platform: "x",
    contentType: "text",
    hashtags: ["Coffee", "Ideas", "Creativity"],
    mentions: []
  },
  {
    id: "post-5",
    content: "Team collaboration at its finest! When diverse minds come together, magic happens. 🌟 #Teamwork #Collaboration #Innovation",
    isSelected: false,
    timestamp: new Date("2024-01-15T16:30:00"),
    characterInfo: {
      id: "char-1",
      name: "Tech Enthusiast",
      avatar: "/images/tech-avatar.png"
    },
    platform: "linkedin",
    contentType: "video",
    hashtags: ["Teamwork", "Collaboration", "Innovation"],
    mentions: ["@teamlead", "@projectmanager"]
  },
  {
    id: "post-6",
    content: "Just finished reading an amazing book on leadership. The key insight: authentic leaders inspire through vulnerability, not perfection. 📚 #Leadership #BookRecommendation #PersonalGrowth",
    isSelected: false,
    timestamp: new Date("2024-01-15T11:45:00"),
    characterInfo: {
      id: "char-3",
      name: "Business Coach",
      avatar: "/images/business-avatar.png"
    },
    platform: "instagram",
    contentType: "image",
    hashtags: ["Leadership", "BookRecommendation", "PersonalGrowth"],
    mentions: []
  },
  {
    id: "post-7",
    content: "Weekend project complete! Built a custom dashboard that tracks our team's productivity metrics. Sometimes the best solutions come from scratching your own itch. 📊 #WeekendProject #DataVisualization #Productivity",
    isSelected: false,
    timestamp: new Date("2024-01-15T13:20:00"),
    platform: "x",
    contentType: "mixed",
    hashtags: ["WeekendProject", "DataVisualization", "Productivity"],
    mentions: ["@analytics", "@devteam"]
  },
  {
    id: "post-8",
    content: "Reminder: Your network is your net worth, but your knowledge is your superpower. Invest in both wisely. 🧠 #Networking #Knowledge #CareerAdvice #ProfessionalDevelopment",
    isSelected: false,
    timestamp: new Date("2024-01-15T15:10:00"),
    characterInfo: {
      id: "char-3",
      name: "Business Coach",
      avatar: "/images/business-avatar.png"
    },
    platform: "linkedin",
    contentType: "text",
    hashtags: ["Networking", "Knowledge", "CareerAdvice", "ProfessionalDevelopment"],
    mentions: []
  },
  {
    id: "post-9",
    content: "The future of work is here: AI doesn't replace humans, it amplifies human creativity. Embrace the tools, enhance your skills. 🤖✨ #FutureOfWork #AI #Creativity #SkillDevelopment",
    isSelected: false,
    timestamp: new Date("2024-01-15T17:30:00"),
    characterInfo: {
      id: "char-1",
      name: "Tech Enthusiast",
      avatar: "/images/tech-avatar.png"
    },
    platform: "x",
    contentType: "video",
    hashtags: ["FutureOfWork", "AI", "Creativity", "SkillDevelopment"],
    mentions: ["@openai", "@microsoft"]
  },
  {
    id: "post-10",
    content: "Design thinking in action: We spent 3 hours understanding the problem before writing a single line of code. Result? A solution that actually works. 🎨 #DesignThinking #ProblemSolving #UserExperience",
    isSelected: false,
    timestamp: new Date("2024-01-15T12:00:00"),
    characterInfo: {
      id: "char-2",
      name: "Creative Director",
      avatar: "/images/creative-avatar.png"
    },
    platform: "instagram",
    contentType: "mixed",
    hashtags: ["DesignThinking", "ProblemSolving", "UserExperience"],
    mentions: ["@designteam"]
  },
  {
    id: "post-11",
    content: "Success tip: Celebrate small wins. They compound into big victories. What small win are you celebrating today? 🎉 #Success #Motivation #SmallWins #Celebration",
    isSelected: false,
    timestamp: new Date("2024-01-15T18:15:00"),
    platform: "instagram",
    contentType: "image",
    hashtags: ["Success", "Motivation", "SmallWins", "Celebration"],
    mentions: []
  },
  {
    id: "post-12",
    content: "Market insight: The companies thriving today aren't just adapting to change—they're creating it. Be a change maker, not a change taker. 📈 #MarketInsights #Innovation #ChangeManagement #Business",
    isSelected: false,
    timestamp: new Date("2024-01-15T19:45:00"),
    characterInfo: {
      id: "char-3",
      name: "Business Coach",
      avatar: "/images/business-avatar.png"
    },
    platform: "linkedin",
    contentType: "text",
    hashtags: ["MarketInsights", "Innovation", "ChangeManagement", "Business"],
    mentions: ["@forbes", "@entrepreneur"]
  }
];

export function Schedule() {
  console.log("Schedule-Old component is rendering");
  
  try {
    // Default to 'day' view on mobile, 'week' on larger screens
    const [view, setView] = useState<ViewType>(() => {
      console.log("Initializing view state");
      return window.innerWidth < 768 ? "day" : "week";
    });
    const [currentDate, setCurrentDate] = useState(new Date());
    let storeValues;
    
    try {
      // For debugging purposes
      if (DEBUG_MODE) console.log("Attempting to access schedule store");
      
      storeValues = useSchedulePostStore();
      console.log("Schedule store loaded:", storeValues ? "yes" : "no");
      
      // For debugging purposes
      if (DEBUG_MODE) {
        console.log("Store values:", Object.keys(storeValues || {}));
      }
    } catch (err) {
      console.error("Error accessing schedule store:", err);
      return (
        <PageLayout icon={CalendarIcon} title="Schedule (Old Version)">
          <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md">
            <h2 className="text-xl font-bold text-red-500 mb-4">Schedule Store Error</h2>
            <p className="text-gray-700 dark:text-gray-300">{String(err)}</p>
          </div>
        </PageLayout>
      );
    }
    const { timeInterval, setTimeInterval } = storeValues;
    const [isBulkCreateOpen, setIsBulkCreateOpen] = useState(false);
    const [isBulkCalendarOpen, setIsBulkCalendarOpen] = useState(false);
    const [timeZone, setTimeZone] = useState(
      Intl.DateTimeFormat().resolvedOptions().timeZone,
    );
    const [showTimeZoneSelector, setShowTimeZoneSelector] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const timeZoneSelectorRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      // Update view based on screen size
      const handleResize = () => {
        if (window.innerWidth < 768 && view !== "day") {
          setView("day");
        }
      };

      window.addEventListener("resize", handleResize);
      handleResize(); // Check initial size

      return () => window.removeEventListener("resize", handleResize);
    }, [view]);

    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (
          timeZoneSelectorRef.current &&
          !timeZoneSelectorRef.current.contains(event.target as Node)
        ) {
          setShowTimeZoneSelector(false);
        }
      };

      document.addEventListener("mousedown", handleClickOutside);
      return () => document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    useEffect(() => {
      // Ensure currentDate reflects timezone changes
      const newDate = new Date(currentDate.toLocaleString("en-US", { timeZone }));
      setCurrentDate(newDate);
    }, [timeZone]);

    const formatTimeZone = useCallback((tz: string) => {
      try {
        const date = new Date();
        const timeZoneOffset = date
          .toLocaleString("en-US", { timeZone: tz, timeZoneName: "short" })
          .split(" ")
          .pop();
        return `${tz.replace(/_/g, " ")} (${timeZoneOffset})`;
      } catch (error) {
        console.error("Time zone format error:", error);
        return tz.replace(/_/g, " ");
      }
    }, []);

    const formatDateRange = useMemo(() => {
      const options: Intl.DateTimeFormatOptions = {
        month: "long",
        day: "numeric",
        year: "numeric",
        timeZone,
      };

      try {
        switch (view) {
          case "day":
            return currentDate.toLocaleDateString(undefined, options);
          case "week": {
            const weekStart = new Date(currentDate);
            weekStart.setDate(currentDate.getDate() - currentDate.getDay());
            const weekEnd = new Date(weekStart);
            weekEnd.setDate(weekStart.getDate() + 6);
            return `${weekStart.toLocaleDateString(undefined, { month: "long", day: "numeric" })} - ${weekEnd.toLocaleDateString(undefined, options)}`;
          }
          case "month":
            return currentDate.toLocaleDateString(undefined, {
              month: "long",
              year: "numeric",
              timeZone,
            });
          default:
            throw new Error("Invalid view type");
        }
      } catch (error) {
        setError("Failed to format date range");
        console.error("Date format error:", error);
        return "";
      }
    }, [currentDate, view, timeZone]);

    const navigateDate = useCallback(
      (direction: "prev" | "next") => {
        try {
          const newDate = new Date(currentDate);
          switch (view) {
            case "day":
              newDate.setDate(
                currentDate.getDate() + (direction === "next" ? 1 : -1),
              );
              break;
            case "week":
              newDate.setDate(
                currentDate.getDate() + (direction === "next" ? 7 : -7),
              );
              break;
            case "month":
              newDate.setMonth(
                currentDate.getMonth() + (direction === "next" ? 1 : -1),
              );
              break;
            default:
              throw new Error("Invalid view type");
          }
          setCurrentDate(newDate);
          setError(null);
          toast.success(`Navigated to ${formatDateRange}`);
        } catch (err) {
          setError("Failed to navigate date");
          toast.error("Error navigating date");
          console.error("Navigation error:", err);
        }
      },
      [currentDate, view, formatDateRange],
    );

    const handleDayClick = useCallback((date: Date) => {
      setCurrentDate(date);
      setView("day");
      setError(null);
    }, []);

    const handleWeekClick = useCallback((date: Date) => {
      // Only allow week view on larger screens
      if (window.innerWidth >= 768) {
        setCurrentDate(date);
        setView("week");
        setError(null);
      } else {
        setCurrentDate(date);
        setView("day");
        setError(null);
      }
    }, []);

    const handleKeyDown = useCallback(
      (event: React.KeyboardEvent) => {
        if (event.key === "ArrowLeft") {
          navigateDate("prev");
        } else if (event.key === "ArrowRight") {
          navigateDate("next");
        }
      },
      [navigateDate],
    );

    const handleBulkScheduleComplete = useCallback(async (scheduledPairs: any[]) => {
      try {
        // Add the scheduled pairs to the schedule store
        const { upsertScheduledPost } = useSchedulePostStore.getState();

        for (const pair of scheduledPairs) {
          if (pair.image && pair.post && pair.scheduledDate && pair.scheduledTime) {
            // Create a proper date object for the scheduled time in local timezone
            const scheduledDateTime = new Date(pair.scheduledDate);
            const [hours, minutes] = pair.scheduledTime.split(':');
            scheduledDateTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);

            const scheduleData = {
              social_network_id: 'x', // Default to X/Twitter
              content_types: ['image'],
              platform: 'x',
              scheduled_at: scheduledDateTime.toISOString(), // Use scheduled_at instead of date_start
              title: `${pair.post.content.substring(0, 30)}...`, // Add title field
              num_occurrences: '1',
              frequency: 'once',
              date_created: new Date().toISOString(),
              content: pair.post.content, // Add content at top level
              post: {
                id: Math.random().toString(36).substr(2, 9),
                content: pair.post.content,
                media_url: pair.image.url,
                hashtags: [],
                mentions: [],
                date_created: new Date().toISOString(),
              }
            };

            await upsertScheduledPost(scheduleData);
          }
        }

        console.log('Bulk schedule completed:', scheduledPairs);
        toast.success(`Successfully scheduled ${scheduledPairs.length} posts!`);
      } catch (error) {
        console.error('Error scheduling posts:', error);
        toast.error('Failed to schedule posts. Please try again.');
      }
    }, []);

    const actions = (
      <div className="flex items-center">
        <button
          onClick={() => setIsBulkCalendarOpen(true)}
          className="flex items-center space-x-2 px-3 py-1.5 sm:px-4 sm:py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 text-sm sm:text-base whitespace-nowrap"
        >
          <CalendarIcon className="w-3 h-3 sm:w-4 sm:h-4" />
          <span>Bulk Calendar</span>
        </button>
      </div>
    );

    return (
      <PageLayout icon={CalendarIcon} title="Schedule" actions={actions}>
        <div className="space-y-4 w-full">
          {/* Controls section */}
          <div className="flex flex-col space-y-3 sm:space-y-0 sm:flex-row justify-between items-start sm:items-center">
            {/* Time interval selector - takes full width on mobile */}
            <div className="w-full sm:w-auto">
              <TimeIntervalSelector
                interval={timeInterval}
                onChange={setTimeInterval}
              />
            </div>

            {/* View and timezone controls - stack on mobile */}
            <div className="flex flex-col space-y-2 sm:space-y-0 sm:flex-row sm:items-center sm:space-x-4 w-full sm:w-auto">
              {/* Timezone selector */}
              <div className="flex items-center">
                <button
                  onClick={() => setShowTimeZoneSelector(!showTimeZoneSelector)}
                  className="flex items-center space-x-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white text-sm"
                >
                  <Globe2 className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span className="text-xs sm:text-sm truncate max-w-[120px]">
                    {timeZone}
                  </span>
                </button>
              </div>

              {/* View selector */}
              <div className="flex items-center space-x-2 bg-gray-100 dark:bg-gray-800 rounded-lg p-1 self-start sm:self-auto">
                <button
                  onClick={() => setView("day")}
                  className={`px-2 py-1 rounded-md text-xs ${
                    view === "day"
                      ? "bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm"
                      : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
                  }`}
                >
                  Day
                </button>
                <button
                  onClick={() => setView("week")}
                  className={`px-2 py-1 rounded-md text-xs ${
                    view === "week"
                      ? "bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm"
                      : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
                  }`}
                >
                  Week
                </button>
                <button
                  onClick={() => setView("month")}
                  className={`px-2 py-1 rounded-md text-xs ${
                    view === "month"
                      ? "bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm"
                      : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
                  }`}
                >
                  Month
                </button>
              </div>
            </div>
          </div>

          {error && (
            <div className="flex items-center space-x-2 text-red-600 dark:text-red-400">
              <AlertCircle className="w-4 h-4" />
              <span>{error}</span>
            </div>
          )}

          {/* Calendar container with proper mobile styling */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden w-full">
            {/* Date navigation controls */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
              <button
                onClick={() => navigateDate("prev")}
                className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-600 dark:text-gray-400">
                  <path d="M15 18l-6-6 6-6" />
                </svg>
              </button>

              <div className="flex items-center space-x-2">
                <h2 className="text-base font-medium text-gray-900 dark:text-white hidden sm:block">
                  {formatDateRange}
                </h2>

                {/* Month and Year selector dropdowns */}
                <div className="flex items-center space-x-2">
                  <select
                    value={currentDate.getMonth()}
                    onChange={(e) => {
                      const newDate = new Date(currentDate);
                      newDate.setMonth(parseInt(e.target.value));
                      setCurrentDate(newDate);
                    }}
                    className="px-2 py-1 rounded-md text-sm border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200"
                  >
                    {[
                      "January", "February", "March", "April", 
                      "May", "June", "July", "August",
                      "September", "October", "November", "December"
                    ].map((month, index) => (
                      <option key={month} value={index}>
                        {month}
                      </option>
                    ))}
                  </select>
                  
                  <select
                    value={currentDate.getFullYear()}
                    onChange={(e) => {
                      const newDate = new Date(currentDate);
                      newDate.setFullYear(parseInt(e.target.value));
                      setCurrentDate(newDate);
                    }}
                    className="px-2 py-1 rounded-md text-sm border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200"
                  >
                    {Array.from(
                      { length: 10 },
                      (_, i) => currentDate.getFullYear() - 5 + i
                    ).map((year) => (
                      <option key={year} value={year}>
                        {year}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <button
                onClick={() => navigateDate("next")}
                className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-600 dark:text-gray-400">
                  <path d="M9 18l6-6-6-6" />
                </svg>
              </button>
            </div>
            
            {view === "day" && <DayView currentDate={currentDate} />}
            {view === "week" && (
              <WeekView currentDate={currentDate} onDayClick={handleDayClick} />
            )}
            {view === "month" && (
              <MonthView
                currentDate={currentDate}
                onDayClick={handleDayClick}
                onWeekClick={handleWeekClick}
              />
            )}
          </div>
        </div>

        {isBulkCreateOpen && (
          <BulkCreateModal
            isOpen={isBulkCreateOpen}
            onClose={() => setIsBulkCreateOpen(false)}
            currentDate={currentDate}
          />
        )}

        {isBulkCalendarOpen && (
          <BulkCalendarModal
            isOpen={isBulkCalendarOpen}
            onClose={() => setIsBulkCalendarOpen(false)}
            currentDate={currentDate}
            availablePosts={sampleBulkPosts}
            onScheduleComplete={handleBulkScheduleComplete}
          />
        )}
      </PageLayout>
    );
  } catch (err) {
    console.error("Error rendering Schedule component:", err);
    return (
      <PageLayout icon={CalendarIcon} title="Schedule (Old Version)">
        <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md">
          <h2 className="text-xl font-bold text-red-500 mb-4">Schedule Rendering Error</h2>
          <p className="text-gray-700 dark:text-gray-300">{String(err)}</p>
        </div>
      </PageLayout>
    );
  }
}
