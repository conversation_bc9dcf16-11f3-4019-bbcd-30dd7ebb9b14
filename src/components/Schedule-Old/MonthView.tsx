import React, { useState, useCallback, useMemo, useEffect } from "react";
import {
  Clock,
  Plus,
  Trash2,
  MessageSquare,
  Image,
  Video,
  Music,
  AlertCircle,
} from "lucide-react";
import { EventModal } from "./EventModal";
import { useSchedulePostStore, platformColors } from "../../stores/useSchedulePostStore.ts";
import { XIcon } from "../icons/XIcon";
import { Instagram, Facebook, Linkedin } from "lucide-react";
import toast from "react-hot-toast";

interface MonthViewProps {
  currentDate: Date;
  onDayClick: (date: Date) => void;
  onWeekClick: (date: Date) => void;
}

interface PlatformSummary {
  platform: string;
  types: { [key: string]: number };
  total: number;
}

const weekDays = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

const platformIcons = {
  x: XIcon,
  instagram: Instagram,
  facebook: Facebook,
  linkedin: Linkedin,
};

function StaticDay({
  date,
  children,
  onAddEvent,
}: {
  date: Date | null;
  children: React.ReactNode;
  onAddEvent: (e: React.MouseEvent, date: Date) => void;
}) {
  return (
    <div
      className="h-full"
      role="gridcell"
      aria-label={date ? `Day ${date.getDate()}` : "Empty day"}
    >
      {children}
      {date && (
        <button
          onClick={(e) => onAddEvent(e, date)}
          className="absolute inset-0 w-full h-full opacity-0 group-hover:opacity-100 flex items-center justify-center bg-indigo-50/50 dark:bg-indigo-900/20 transition-opacity focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-indigo-500"
          aria-label={`Add event on ${date.toDateString()}`}
        >
          <Plus className="w-4 h-4 text-indigo-600 dark:text-indigo-400" />
        </button>
      )}
    </div>
  );
}

export function MonthView({
  currentDate,
  onDayClick,
  onWeekClick,
}: MonthViewProps) {
  const {
    scheduledPosts = [],
    fetchScheduledPosts,
    upsertScheduledPost,
    deleteScheduledPost,
  } = useSchedulePostStore() || {};

  const events = useMemo(() => {
    return Array.isArray(scheduledPosts)
      ? scheduledPosts.map((post) => ({
          id: post.id,
          title: post.title || `Post - ${post.platform || "unknown"}`,
          date: new Date(post.scheduled_at),
          time: new Date(post.scheduled_at).toLocaleTimeString([], {
            hour: "numeric",
            minute: "2-digit",
            hour12: true,
          }),
          platform: post.platform || "unknown",
          contentTypes: post.content_types || ["text"],
          content: post.content,
        }))
      : [];
  }, [scheduledPosts]);

  useEffect(() => {
    if (fetchScheduledPosts) {
      console.log("[MonthView] Fetching scheduled posts...");
      fetchScheduledPosts().catch((err) =>
        console.error("[MonthView] Error fetching scheduled posts:", err)
      );
    }
  }, [fetchScheduledPosts]);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedEventId, setSelectedEventId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const getDaysInMonth = useMemo(() => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDay = firstDay.getDay();

    const days: (Date | null)[] = [];
    let day = 1;

    for (let i = 0; i < 42; i++) {
      if (i < startingDay || day > daysInMonth) {
        days.push(null);
      } else {
        days.push(new Date(year, month, day++));
      }
    }

    return days;
  }, [currentDate]);

  const getEventsForDate = useCallback(
    (date: Date | null) => {
      // Return empty array if date is null or events array is not available
      if (!date || !Array.isArray(events)) return [];
      
      try {
        return events.filter((event) => {
          try {
            // Ensure event.date is a Date object
            const eventDate = event.date instanceof Date ? event.date : new Date(event.date);
            
            return (
              eventDate.getDate() === date.getDate() &&
              eventDate.getMonth() === date.getMonth() &&
              eventDate.getFullYear() === date.getFullYear()
            );
          } catch (err) {
            console.error("[MonthView] Error comparing dates:", err);
            return false;
          }
        });
      } catch (err) {
        console.error("[MonthView] Error in getEventsForDate:", err);
        return [];
      }
    },
    [events],
  );

  const getPlatformSummary = useCallback(
    (dateEvents: any[]): PlatformSummary[] => {
      const summary = dateEvents.reduce(
        (acc: { [key: string]: PlatformSummary }, event) => {
          const platform = event.platform || "other";
          if (!acc[platform]) {
            acc[platform] = { platform, types: {}, total: 0 };
          }

          (event.contentTypes || ["text"]).forEach((type: string) => {
            acc[platform].types[type] = (acc[platform].types[type] || 0) + 1;
          });

          acc[platform].total++;
          return acc;
        },
        {},
      );

      return Object.values(summary).sort((a, b) => b.total - a.total);
    },
    [],
  );

  const getContentTypeIcon = useCallback((type: string) => {
    switch (type.toLowerCase()) {
      case "text":
        return <MessageSquare className="w-3 h-3" aria-label="Text" />;
      case "image":
        return <Image className="w-3 h-3" aria-label="Image" />;
      case "video":
        return <Video className="w-3 h-3" aria-label="Video" />;
      case "music":
        return <Music className="w-3 h-3" aria-label="Music" />;
      default:
        return null;
    }
  }, []);

  const handleAddEvent = useCallback(
    (e: React.MouseEvent, date: Date) => {
      e.stopPropagation();
      try {
        const newEvent = {
          id: crypto.randomUUID(),
          title: "New Event",
          date: new Date(date.setHours(9)),
          time: "9:00 AM",
        };

        upsertScheduledPost(newEvent);
        setSelectedEventId(newEvent.id);
        setIsModalOpen(true);
        setError(null);
        toast.success("Event created");
      } catch (err) {
        setError("Failed to create event");
        toast.error("Error creating event");
        console.error("Add event error:", err);
      }
    },
    [upsertScheduledPost],
  );

  const handleEventClick = useCallback(
    (e: React.MouseEvent, eventId: string) => {
      e.stopPropagation();
      setSelectedEventId(eventId);
      setIsModalOpen(true);
    },
    [],
  );

  const handleModalSave = useCallback(
    (data: { types: string[]; platform: string; content?: any }) => {
      if (!selectedEventId) return;
      try {
        const typeLabels = data.types
          .map((type) => type.charAt(0).toUpperCase() + type.slice(1))
          .join(" + ");
        upsertScheduledPost({
          id: selectedEventId,
          title: `${typeLabels} - ${data.platform}`,
          contentTypes: data.types,
          platform: data.platform,
          content: data.content,
        });
        toast.success("Event updated");
        setError(null);
      } catch (err) {
        setError("Failed to save event");
        toast.error("Error saving event");
        console.error("Save error:", err);
      }
    },
    [selectedEventId, upsertScheduledPost],
  );

  const selectedEvent = useMemo(
    () =>
      selectedEventId
        ? events.find((event) => event.id === selectedEventId)
        : undefined,
    [selectedEventId, events],
  );

  return (
    <div
      role="grid"
      aria-label={`Monthly schedule for ${currentDate.toLocaleString("default", { month: "long", year: "numeric" })}`}
    >
        {error && (
          <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/30 rounded-lg flex items-center space-x-2">
            <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
            <span className="text-sm text-red-600 dark:text-red-400">
              {error}
            </span>
          </div>
        )}

        <div className="grid grid-cols-7 gap-px mb-4">
          {weekDays.map((day) => (
            <div
              key={day}
              className="text-sm font-medium text-center text-gray-500 dark:text-gray-400 py-2 hover:text-indigo-600 dark:hover:text-indigo-400 cursor-pointer focus:outline-none focus:ring-2 focus:ring-indigo-500 rounded"
              onClick={() => {
                const today = new Date(currentDate);
                today.setDate(
                  today.getDate() - today.getDay() + weekDays.indexOf(day),
                );
                onWeekClick(today);
              }}
              tabIndex={0}
              role="columnheader"
              aria-label={`${day} column`}
              onKeyPress={(e) =>
                e.key === "Enter" &&
                onWeekClick(
                  new Date(
                    currentDate.setDate(
                      currentDate.getDate() -
                        currentDate.getDay() +
                        weekDays.indexOf(day),
                    ),
                  ),
                )
              }
            >
              {day}
            </div>
          ))}
        </div>

        <div className="grid grid-cols-7 gap-px bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden">
          {getDaysInMonth.map((date, index) => {
            const dateEvents = date ? getEventsForDate(date) : [];
            const isCurrentMonth =
              date && date.getMonth() === currentDate.getMonth();
            const platformSummary = getPlatformSummary(dateEvents);
            const isToday =
              date && date.toDateString() === new Date().toDateString();

            return (
              <div
                key={index}
                className={`min-h-[120px] bg-white dark:bg-gray-800 p-2 relative group ${
                  date
                    ? "hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer"
                    : "bg-gray-50 dark:bg-gray-700/50"
                } ${!isCurrentMonth ? "opacity-50" : ""} ${isToday ? "border border-indigo-400" : ""}`}
                onClick={() => date && onDayClick(date)}
                tabIndex={date ? 0 : -1}
                onKeyPress={(e) =>
                  date && e.key === "Enter" && onDayClick(date)
                }
              >
                <StaticDay
                  date={date}
                  onAddEvent={handleAddEvent}
                >
                  {date && (
                    <>
                      <div className="flex items-center justify-between mb-1">
                        <div
                          className={`text-sm font-medium ${
                            isToday
                              ? "text-indigo-600 dark:text-indigo-400"
                              : "text-gray-900 dark:text-white"
                          }`}
                        >
                          {date.getDate()}
                        </div>
                      </div>
                      {platformSummary.length > 0 && (
                        <div className="mt-1 space-y-1">
                          {platformSummary.map(({ platform, types, total }) => {
                            const colors = platformColors[
                              platform as keyof typeof platformColors
                            ] || {
                              bg: "bg-indigo-100 dark:bg-indigo-900/50",
                              text: "text-indigo-600 dark:text-indigo-400",
                            };
                            const Icon =
                              platformIcons[
                                platform as keyof typeof platformIcons
                              ];

                            return (
                              <div
                                key={platform}
                                className={`${colors.bg} ${colors.text} px-2 py-1.5 rounded text-xs hover:ring-1 ${colors.hover} cursor-pointer`}
                                onClick={(e) =>
                                  handleEventClick(e, dateEvents[0].id)
                                }
                                role="button"
                                tabIndex={0}
                                aria-label={`${platform} events: ${total}`}
                              >
                                <div className="flex items-center justify-between mb-1">
                                  <div className="flex items-center space-x-1">
                                    {Icon && <Icon className="w-3 h-3" />}
                                    <span className="capitalize">
                                      {platform}
                                    </span>
                                  </div>
                                  <span className="font-medium">{total}</span>
                                </div>
                                <div className="flex flex-wrap gap-2">
                                  {Object.entries(types).map(
                                    ([type, count]) => (
                                      <div
                                        key={type}
                                        className="flex items-center space-x-1 bg-white/10 dark:bg-black/10 px-1.5 py-0.5 rounded"
                                      >
                                        {getContentTypeIcon(type)}
                                        <span>{count}</span>
                                      </div>
                                    ),
                                  )}
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      )}
                    </>
                  )}
                </StaticDay>
              </div>
            );
          })}
        </div>

        <EventModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setSelectedEventId(null);
            setError(null);
          }}
          onSave={handleModalSave}
          onDelete={
            selectedEventId
              ? () => {
                  try {
                    deleteScheduledPost(selectedEventId);
                    setIsModalOpen(false);
                    toast.success("Event deleted");
                  } catch (err) {
                    setError("Failed to delete event");
                    toast.error("Error deleting event");
                    console.error("Delete error:", err);
                  }
                }
              : undefined
          }
          isExisting={!!selectedEventId}
          event={selectedEvent}
        />
      </div>
  );
}
