import React, { useState, useEffect, useMemo } from "react";
import {
  X as XIcon,
  Image as ImageIcon,
  MessageSquare,
  Plus,
  Loader2,
  Hash,
  AtSign,
  AlertCircle,
  HelpCircle,
  ChevronDown,
  Book,
  Lightbulb,
  Sparkles,
  Bot,
  Eye,
} from "lucide-react";
import { XIcon as XPlatformIcon } from "../icons/XIcon";
import { Character, getCharacters } from "../../services/characters";

import toast from "react-hot-toast";
import { useForm } from "react-hook-form";
import FormInput from "../FormInput";
import { PreviewModal } from "../PreviewModal";
import { InputTags } from "../InputTags";

// Simple tooltip component
function Tooltip({ description }: { description: string }) {
  return (
    <div className="group relative inline-block ml-1">
      <HelpCircle className="w-4 h-4 text-gray-400 hover:text-gray-500 dark:text-gray-400 dark:hover:text-gray-300 cursor-help" />
      <div className="pointer-events-none invisible group-hover:visible opacity-0 group-hover:opacity-100 transition-opacity absolute z-[9999] w-48">
        <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 px-2 py-1">
          <div className="relative bg-gray-900 dark:bg-gray-700 text-white text-xs rounded-lg px-2 py-1 whitespace-normal shadow-lg">
            {description}
            <div className="absolute left-1/2 -translate-x-1/2 top-full w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-t-[6px] border-t-gray-900 dark:border-t-gray-700" />
          </div>
        </div>
      </div>
    </div>
  );
}

interface EventModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: { types: string[]; platform: string; content?: any }) => void;
  onDelete?: () => void;
  isExisting?: boolean;
  event?: any;
}

const platforms = [
  { id: "x", name: "X (Twitter)", icon: XPlatformIcon, available: true },
];

const contentTypes = [
  { id: "text", icon: MessageSquare, label: "Text Post", isMedia: false },
  { id: "image", icon: ImageIcon, label: "Image Post", isMedia: true },
];

const suggestedHashtags = {
  tech: [
    "#TechNews",
    "#Innovation",
    "#AI",
    "#DigitalTransformation",
    "#FutureOfWork",
  ],
  marketing: [
    "#MarketingTips",
    "#SocialMedia",
    "#ContentCreation",
    "#GrowthHacking",
    "#DigitalMarketing",
  ],
  business: [
    "#Startup",
    "#Entrepreneurship",
    "#BusinessGrowth",
    "#Leadership",
    "#Success",
  ],
};

export function EventModal({
  isOpen,
  onClose,
  onSave,
  onDelete,
  isExisting = false,
  event,
}: EventModalProps) {
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
  const [selectedPlatform, setSelectedPlatform] = useState("");
  const [content, setContent] = useState<{
    text?: string;
    prompt?: string;
    mediaUrl?: string;
    mediaType?: "image";
    hashtags?: string[];
    mentions?: string[];
  }>({});
  const [isGenerating, setIsGenerating] = useState(false);

  const [additionalContent, setAdditionalContent] = useState<string[]>([]);
  const [hashtags, setHashtags] = useState<string[]>([]);
  const [mentions, setMentions] = useState<string[]>([]);

  const [showPromptTemplates, setShowPromptTemplates] = useState(false);
  const [characters, setCharacters] = useState<Character[]>([]);
  const [selectedCharacter, setSelectedCharacter] = useState<Character | null>(
    null,
  );
  const [showCharacterDropdown, setShowCharacterDropdown] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    setAdditionalContent([
      "\n\n",
      hashtags.map((h: string) => `#${h}`).join(" "),
      "\n",
      mentions.map((m: string) => `@${m}`).join(" "),
    ]);
  }, [hashtags, mentions]);

  useEffect(() => {
    const loadCharacters = async () => {
      try {
        const data = await getCharacters();
        setCharacters(data);
      } catch (error) {
        toast.error("Failed to load characters");
      }
    };

    loadCharacters();
  }, []);

  useEffect(() => {
    if (event) {
      setSelectedTypes(event.contentTypes || []);
      setSelectedPlatform(event.platform || "");
      setContent(event.content || {});
    }
  }, [event]);

  useEffect(() => {
    if (!isOpen) {
      resetForm();
    }
  }, [isOpen]);

  const resetForm = () => {
    setSelectedTypes([]);
    setSelectedPlatform("");
    setContent({});
    setShowPromptTemplates(false);
    setSelectedCharacter(null);
    setError(null);
  };

  const validateForm = () => {
    if (selectedTypes.length === 0) {
      setError("Please select at least one content type");
      return false;
    }
    if (!selectedPlatform) {
      setError("Please select a platform");
      return false;
    }
    // if (!content.prompt?.trim()) {
    //   setError("Content is required");
    //   return false;
    // }
    if (selectedTypes.includes("image") && !content.mediaUrl) {
      setError("Media URL is required for image posts");
      return false;
    }
    setError(null);
    return true;
  };

  const handleSelectCharacter = (character: Character) => {
    setSelectedCharacter(character);
    setContent((prev) => ({
      ...prev,
      prompt: character.prompt,
      style: character.style,
    }));
    setShowCharacterDropdown(false);
    toast.success(`Character "${character.name}" selected`);
  };

  const handleTypeToggle = (typeId: string) => {
    const typeConfig = contentTypes.find((t) => t.id === typeId);
    if (!typeConfig) return;

    setSelectedTypes((prev) => {
      if (prev.includes(typeId)) {
        return prev.filter((id) => id !== typeId);
      }
      if (typeConfig.isMedia) {
        const withoutMedia = prev.filter(
          (id) => !contentTypes.find((t) => t.id === id)?.isMedia,
        );
        return [...withoutMedia, typeId];
      }
      return [...prev, typeId];
    });
  };

  const handleGenerateContent = async () => {
    // if (!content.prompt) {
    //   setError("Please enter a prompt");
    //   return;
    // }

    setIsGenerating(true);
    setError(null);

    try {
      await new Promise((resolve) => setTimeout(resolve, 1500));

      const generatedContent = {
        text: selectedTypes.includes("text")
          ? "✨ Generated post content would appear here"
          : undefined,
        mediaUrl: selectedTypes.includes("image")
          ? "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&auto=format&fit=crop"
          : undefined,
        hashtags: ["AI", "Generated", "Content"],
        mentions: [],
      };

      setContent((prev) => ({
        ...prev,
        ...generatedContent,
      }));

      toast.success("Content generated successfully");
    } catch (err) {
      setError("Failed to generate content");
      toast.error("Error generating content");
      console.error("Generation error:", err);
    } finally {
      setIsGenerating(false);
    }
  };

  const {
    handleSubmit,
    control,
    // setError,
    // clearErrors,
    watch,
    formState: { errors, touchedFields },
  } = useForm();

  const postContent = watch("postContent");
  const mediaUrl = watch("mediaUrl");

  const onSubmit = (data: any) => {
    if (additionalContent) {
      data.postContent += additionalContent;
    }
    console.log(data);
    return;
    try {
      if (!validateForm()) return;

      onSave({
        types: selectedTypes,
        platform: selectedPlatform,
        content,
      });

      toast.success("Event saved successfully");
      onClose();
    } catch (err) {
      setError("Failed to save event");
      toast.error("Error saving event");
      console.error("Save error:", err);
    }
  };

  if (!isOpen) return null;

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4 overflow-y-auto">
        <div className="bg-white dark:bg-gray-800 rounded-xl w-full max-w-3xl m-4">
          <div className="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              {isExisting ? "Edit Post" : "Create Post"}
            </h2>
            <button
              type="button"
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <XIcon className="w-5 h-5" />
            </button>
          </div>

          <div className="p-6 space-y-6 max-h-[calc(100vh-16rem)] overflow-y-auto">
            {error && (
              <div className="p-4 bg-red-50 dark:bg-red-900/30 rounded-lg flex items-center space-x-2">
                <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
                <span className="text-sm text-red-600 dark:text-red-400">
                  {error}
                </span>
              </div>
            )}

            {/* Content */}
            <div className="space-y-4">
              {/* Platform Selection */}
              <div>
                <FormInput
                  control={control}
                  label="Platform"
                  name="platform"
                  type="select"
                  rules={{ required: "Name on card is required" }}
                  placeholder="Select Platform"
                  includeWrapper={false}
                  options={platforms.reduce(
                    (acc, item) => {
                      acc[item.id] = item.name;
                      return acc;
                    },
                    {} as Record<string, string>,
                  )}
                  error={
                    touchedFields.platform &&
                    errors.platform?.message?.toString()
                  }
                />
              </div>

              {/* Content Type Selection */}
              <div>
                <label className="block text-md font-medium text-gray-500 dark:text-gray-400 mb-2">
                  Content Type
                </label>
                <div className="grid grid-cols-2 gap-3">
                  {contentTypes.map((type) => (
                    <button
                      type="button"
                      key={type.id}
                      onClick={() => handleTypeToggle(type.id)}
                      className={`flex items-center space-x-2 p-3 rounded-lg border ${
                        selectedTypes.includes(type.id)
                          ? "border-indigo-600 bg-indigo-50 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400"
                          : "border-gray-200 dark:border-gray-700 hover:border-indigo-300 dark:hover:border-indigo-600 text-gray-500 dark:text-gray-400"
                      }`}
                    >
                      <type.icon className="w-5 h-5" />
                      <span className="font-medium">{type.label}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Character Selection */}
              <div>
                <label className="block text-md font-medium text-gray-500 dark:text-gray-400 mb-2">
                  Character
                  <Tooltip description="Select a character to use their style and prompts" />
                </label>
                <div className="relative">
                  <button
                    type="button"
                    onClick={() =>
                      setShowCharacterDropdown(!showCharacterDropdown)
                    }
                    className="w-full flex items-center justify-between px-4 py-2 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-indigo-300 dark:hover:border-indigo-600 transition-colors"
                  >
                    <div className="flex-1 text-center">
                      {selectedCharacter ? (
                        <div className="flex items-center justify-center space-x-3">
                          <div className="w-8 h-8 rounded-lg bg-indigo-100 dark:bg-indigo-900/30 flex items-center justify-center">
                            <Bot className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
                          </div>
                          <div>
                            <div className="font-medium text-gray-900 dark:text-white">
                              {selectedCharacter.name}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {selectedCharacter.style.join(", ")}
                            </div>
                          </div>
                        </div>
                      ) : (
                        <span className="text-md text-gray-500 dark:text-gray-400">
                          Select a character
                        </span>
                      )}
                    </div>
                    <ChevronDown className="w-5 h-5 text-gray-400 ml-2" />
                  </button>

                  {showCharacterDropdown && (
                    <div className="absolute left-0 right-0 mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-10">
                      {characters.map((character) => (
                        <button
                          type="button"
                          key={character.id}
                          onClick={() => handleSelectCharacter(character)}
                          className="w-full px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center justify-center space-x-3"
                        >
                          <div className="w-8 h-8 rounded-lg bg-indigo-100 dark:bg-indigo-900/30 flex items-center justify-center flex-shrink-0">
                            <Bot className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
                          </div>
                          <div className="flex-1 text-center">
                            <div className="font-medium text-gray-900 dark:text-white">
                              {character.name}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400 line-clamp-2">
                              {character.description}
                            </div>
                            <div className="mt-1 flex flex-wrap justify-center gap-1">
                              {character.style.map((style) => (
                                <span
                                  key={style}
                                  className="px-1.5 py-0.5 text-xs bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 rounded"
                                >
                                  {style}
                                </span>
                              ))}
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Content Generation */}
              {selectedTypes.length > 0 && watch("platform") && (
                <div className="space-y-4">
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Content
                      </label>
                      <div className="flex space-x-2">
                        <PreviewModal
                          icon={Eye}
                          label="Preview"
                          modalLabel="Content Preview"
                          mediaUrl={
                            selectedTypes.includes("image") ? mediaUrl : ""
                          }
                          content={postContent}
                          additionalContent={additionalContent}
                        />

                        <button
                          type="button"
                          onClick={() => setShowPromptTemplates(true)}
                          className="flex items-center space-x-1 text-sm text-indigo-600 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300"
                        >
                          <Book className="w-4 h-4" />
                          <span>Templates</span>
                        </button>
                      </div>
                    </div>
                    <FormInput
                      control={control}
                      name="postContent"
                      type="textarea"
                      maxLength={200}
                      rules={{ required: "Content is required" }}
                      placeholder="Enter your content..."
                      includeWrapper={false}
                      error={
                        touchedFields.postContent &&
                        errors.postContent?.message?.toString()
                      }
                    />

                    {selectedTypes.includes("image") && (
                      <div className="mt-2">
                        <FormInput
                          control={control}
                          type="url"
                          name="mediaUrl"
                          rules={{ required: "Image URL is required" }}
                          placeholder="Enter image URL"
                          includeWrapper={false}
                          error={
                            touchedFields.mediaUrl &&
                            errors.mediaUrl?.message?.toString()
                          }
                        />
                      </div>
                    )}
                    <div className="mt-2 flex justify-end">
                      <button
                        type="button"
                        onClick={handleGenerateContent}
                        disabled={!postContent || isGenerating}
                        className="flex items-center space-x-2 px-3 py-1.5 text-sm bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50"
                      >
                        {isGenerating ? (
                          <>
                            <Loader2 className="w-4 h-4 animate-spin" />
                            <span>Generating...</span>
                          </>
                        ) : (
                          <>
                            <Sparkles className="w-4 h-4" />
                            <span>Generate</span>
                          </>
                        )}
                      </button>
                    </div>
                  </div>

                  {/* Hashtags */}
                  <InputTags
                    name="hashtags"
                    label="Hashtags"
                    placeholder="Add hashtag"
                    inputIcon={Hash}
                    suggestionIcon={Lightbulb}
                    suggestions={suggestedHashtags}
                    onCallback={setHashtags}
                  />

                  {/* Mentions */}
                  <InputTags
                    name="mentions"
                    label="Mentions"
                    placeholder="Add mention"
                    inputIcon={AtSign}
                    onCallback={setMentions}
                  />
                </div>
              )}
            </div>
          </div>

          <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
            {isExisting && onDelete && (
              <button
                type="button"
                onClick={onDelete}
                className="px-4 py-2 text-sm font-medium text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30 rounded-lg"
              >
                Delete
              </button>
            )}
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg"
            >
              Cancel
            </button>
            <button
              type="submit"
              // disabled={selectedTypes.length === 0 || !selectedPlatform}
              disabled={isLoading}
              className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 rounded-lg disabled:opacity-50"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Saving post...</span>
                </>
              ) : (
                <>{isExisting ? "Save Changes" : "Create"}</>
              )}
            </button>
          </div>
        </div>

        {/* Template Selector Modal */}
        {showPromptTemplates && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-60">
            <PromptTemplateSelector
              contentType={selectedTypes.includes("image") ? "image" : "text"}
              onSelect={(template) => {
                setContent((prev) => ({
                  ...prev,
                  prompt: template.template,
                }));
                setShowPromptTemplates(false);
                toast.success("Template applied");
              }}
              onClose={() => setShowPromptTemplates(false)}
            />
          </div>
        )}
      </div>
    </form>
  );
}
