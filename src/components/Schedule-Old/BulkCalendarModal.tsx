import React, { useState, useEffect } from 'react';
import {
  Calendar, Clock, X as XIcon, Image as ImageIcon,
  MessageSquare, Plus, Check, ChevronLeft, ChevronRight,
  Filter, Upload, Sparkles, AlertCircle, Loader2, User, FileText
} from 'lucide-react';
import { Photo } from '../Photo/index';

interface GeneratedPost {
  id: string;
  content: string;
  isSelected: boolean;
  timestamp: Date;
  characterInfo?: {
    id: string;
    name: string;
    avatar: string;
  };
  platform?: string;
  contentType?: 'text' | 'image' | 'video' | 'mixed';
  hashtags?: string[];
  mentions?: string[];
}

interface ImagePostPair {
  id: string;
  image: Photo | null;
  post: GeneratedPost | null;
  scheduledDate?: Date;
  scheduledTime?: string;
}

interface BulkCalendarModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentDate: Date;
  availablePosts?: GeneratedPost[];
  onScheduleComplete?: (scheduledPairs: ImagePostPair[]) => void;
}

export function BulkCalendarModal({
  isOpen,
  onClose,
  currentDate,
  availablePosts = [],
  onScheduleComplete
}: BulkCalendarModalProps) {
  const [step, setStep] = useState<'select-images' | 'select-posts' | 'pair-content' | 'schedule'>('select-images');
  const [selectedImages, setSelectedImages] = useState<Photo[]>([]);
  const [selectedPosts, setSelectedPosts] = useState<GeneratedPost[]>([]);
  const [pairs, setPairs] = useState<ImagePostPair[]>([]);
  const [portfolioPhotos, setPortfolioPhotos] = useState<Photo[]>([]);
  const [imageFilter, setImageFilter] = useState<'all' | 'uploaded' | 'generated'>('all');
  const [isLoading, setIsLoading] = useState(false);
  const [timeInterval, setTimeInterval] = useState<15 | 30 | 60>(30); // Default to 30 minutes
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [scheduledPairs, setScheduledPairs] = useState<ImagePostPair[]>([]);
  const [postFilter, setPostFilter] = useState<{
    character: string;
    dateRange: string;
    searchText: string;
  }>({
    character: 'all',
    dateRange: 'all',
    searchText: ''
  });

  // Sample photos data (normally would come from API)
  const samplePhotos: Photo[] = [
    {
      id: "1",
      title: "Modern Tech Workspace",
      resolution: "3840x2160",
      format: "JPEG",
      style: ["Modern", "Corporate", "Minimalist"],
      aspectRatio: "16:9",
      thumbnailUrl: "https://images.unsplash.com/photo-1497215728101-856f4ea42174?w=800&auto=format&fit=crop",
      url: "https://images.unsplash.com/photo-1497215728101-856f4ea42174?w=1920&auto=format&fit=crop",
      isGenerated: false,
    },
    {
      id: "2",
      title: "Urban Coffee Experience",
      resolution: "2560x1440",
      format: "JPEG",
      style: ["Urban", "Lifestyle", "Warm"],
      aspectRatio: "16:9",
      thumbnailUrl: "https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=800&auto=format&fit=crop",
      url: "https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=1920&auto=format&fit=crop",
      isGenerated: true,
    },
    {
      id: "3",
      title: "Natural Landscape",
      resolution: "3840x2160",
      format: "JPEG",
      style: ["Natural", "Cinematic", "Peaceful"],
      aspectRatio: "16:9",
      thumbnailUrl: "https://images.unsplash.com/photo-1472214103451-9374bd1c798e?w=800&auto=format&fit=crop",
      url: "https://images.unsplash.com/photo-1472214103451-9374bd1c798e?w=1920&auto=format&fit=crop",
      isGenerated: false,
    },
    {
      id: "4",
      title: "Urban Night Life",
      resolution: "2560x1440",
      format: "JPEG",
      style: ["Urban", "Night", "Dynamic"],
      aspectRatio: "16:9",
      thumbnailUrl: "https://images.unsplash.com/photo-1519501025264-65ba15a82390?w=800&auto=format&fit=crop",
      url: "https://images.unsplash.com/photo-1519501025264-65ba15a82390?w=1920&auto=format&fit=crop",
      isGenerated: true,
    },
    {
      id: "5",
      title: "Futuristic Tech",
      resolution: "3840x2160",
      format: "JPEG",
      style: ["Futuristic", "Technology", "Modern"],
      aspectRatio: "16:9",
      thumbnailUrl: "https://images.unsplash.com/photo-1535223289827-42f1e9919769?w=800&auto=format&fit=crop",
      url: "https://images.unsplash.com/photo-1535223289827-42f1e9919769?w=1920&auto=format&fit=crop",
      isGenerated: true,
    },
    {
      id: "6",
      title: "Creative Design Studio",
      resolution: "2560x1440",
      format: "JPEG",
      style: ["Creative", "Design", "Artistic"],
      aspectRatio: "16:9",
      thumbnailUrl: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&auto=format&fit=crop",
      url: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=1920&auto=format&fit=crop",
      isGenerated: false,
    },
    {
      id: "7",
      title: "AI Generated Portrait",
      resolution: "3840x2160",
      format: "JPEG",
      style: ["Portrait", "AI", "Digital"],
      aspectRatio: "4:3",
      thumbnailUrl: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=800&auto=format&fit=crop",
      url: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=1920&auto=format&fit=crop",
      isGenerated: true,
    },
    {
      id: "8",
      title: "Business Meeting",
      resolution: "2560x1440",
      format: "JPEG",
      style: ["Business", "Professional", "Corporate"],
      aspectRatio: "16:9",
      thumbnailUrl: "https://images.unsplash.com/photo-1552664730-d307ca884978?w=800&auto=format&fit=crop",
      url: "https://images.unsplash.com/photo-1552664730-d307ca884978?w=1920&auto=format&fit=crop",
      isGenerated: false,
    },
    {
      id: "9",
      title: "Abstract Digital Art",
      resolution: "3840x2160",
      format: "JPEG",
      style: ["Abstract", "Digital", "Colorful"],
      aspectRatio: "1:1",
      thumbnailUrl: "https://images.unsplash.com/photo-1541701494587-cb58502866ab?w=800&auto=format&fit=crop",
      url: "https://images.unsplash.com/photo-1541701494587-cb58502866ab?w=1920&auto=format&fit=crop",
      isGenerated: true,
    },
    {
      id: "10",
      title: "Team Collaboration",
      resolution: "2560x1440",
      format: "JPEG",
      style: ["Team", "Collaboration", "Workspace"],
      aspectRatio: "16:9",
      thumbnailUrl: "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=800&auto=format&fit=crop",
      url: "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=1920&auto=format&fit=crop",
      isGenerated: false,
    },
    {
      id: "11",
      title: "AI Generated Landscape",
      resolution: "3840x2160",
      format: "JPEG",
      style: ["Landscape", "AI", "Surreal"],
      aspectRatio: "16:9",
      thumbnailUrl: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&auto=format&fit=crop",
      url: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&auto=format&fit=crop",
      isGenerated: true,
    },
    {
      id: "12",
      title: "Product Photography",
      resolution: "2560x1440",
      format: "JPEG",
      style: ["Product", "Commercial", "Clean"],
      aspectRatio: "4:3",
      thumbnailUrl: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=800&auto=format&fit=crop",
      url: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=1920&auto=format&fit=crop",
      isGenerated: false,
    }
  ];

  // Load portfolio photos on mount
  useEffect(() => {
    if (isOpen) {
      setIsLoading(true);
      // Simulate loading portfolio photos
      setTimeout(() => {
        setPortfolioPhotos(samplePhotos);
        setIsLoading(false);
      }, 500);
    }
  }, [isOpen]);

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setStep('select-images');
      setSelectedImages([]);
      setSelectedPosts([]);
      setPairs([]);
    }
  }, [isOpen]);

  const filteredImages = portfolioPhotos.filter(photo => {
    if (imageFilter === 'all') return true;
    if (imageFilter === 'uploaded') return !photo.isGenerated;
    if (imageFilter === 'generated') return photo.isGenerated;
    return true;
  });

  const filteredPosts = availablePosts.filter(post => {
    // Character filter - matches BulkGenerations logic
    if (postFilter.character === 'character') {
      if (!post.characterInfo) return false;
    } else if (postFilter.character === 'general') {
      if (post.characterInfo) return false;
    } else if (postFilter.character !== 'all' && postFilter.character !== 'character' && postFilter.character !== 'general') {
      // Specific character selected
      if (!post.characterInfo || post.characterInfo.name !== postFilter.character) {
        return false;
      }
    }

    // Date range filter - matches BulkGenerations logic
    if (postFilter.dateRange !== 'all') {
      const now = new Date();
      const postDate = new Date(post.timestamp);

      if (postFilter.dateRange === 'today') {
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const postDay = new Date(postDate.getFullYear(), postDate.getMonth(), postDate.getDate());
        if (postDay.getTime() !== today.getTime()) return false;
      } else if (postFilter.dateRange === 'week') {
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        if (postDate < weekAgo) return false;
      } else if (postFilter.dateRange === 'month') {
        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        if (postDate < monthAgo) return false;
      }
    }

    // Search text filter
    if (postFilter.searchText.trim()) {
      const searchLower = postFilter.searchText.toLowerCase();
      const contentMatch = post.content.toLowerCase().includes(searchLower);
      const characterMatch = post.characterInfo?.name.toLowerCase().includes(searchLower);

      if (!contentMatch && !characterMatch) {
        return false;
      }
    }

    return true;
  });

  // Get unique characters for filter options
  const uniqueCharacters = Array.from(new Set(
    availablePosts
      .filter(post => post.characterInfo)
      .map(post => post.characterInfo!.name)
  ));

  const handleImageSelect = (photo: Photo) => {
    if (selectedImages.length >= 10 && !selectedImages.find(img => img.id === photo.id)) {
      return; // Max 10 images
    }

    setSelectedImages(prev => {
      const exists = prev.find(img => img.id === photo.id);
      let newImages;

      if (exists) {
        newImages = prev.filter(img => img.id !== photo.id);
      } else {
        newImages = [...prev, photo];
      }

      // If we're removing images and have more posts than images, trim posts
      if (newImages.length < selectedPosts.length) {
        setSelectedPosts(currentPosts => currentPosts.slice(0, newImages.length));
      }

      return newImages;
    });
  };

  const handlePostSelect = (post: GeneratedPost) => {
    // Limit posts to the number of selected images (max 10)
    const maxPosts = Math.min(selectedImages.length, 10);
    if (selectedPosts.length >= maxPosts && !selectedPosts.find(p => p.id === post.id)) {
      return; // Max posts limited by selected images
    }

    setSelectedPosts(prev => {
      const exists = prev.find(p => p.id === post.id);
      if (exists) {
        return prev.filter(p => p.id !== post.id);
      } else {
        return [...prev, post];
      }
    });
  };

  const handleNextStep = () => {
    if (step === 'select-images' && selectedImages.length > 0) {
      setStep('select-posts');
    } else if (step === 'select-posts' && selectedPosts.length > 0) {
      // Initialize pairs
      const maxPairs = Math.min(selectedImages.length, selectedPosts.length);
      const initialPairs: ImagePostPair[] = [];
      
      for (let i = 0; i < maxPairs; i++) {
        initialPairs.push({
          id: `pair-${i}`,
          image: selectedImages[i] || null,
          post: selectedPosts[i] || null,
        });
      }
      
      setPairs(initialPairs);
      setStep('pair-content');
    } else if (step === 'pair-content') {
      setStep('schedule');
    }
  };

  const handlePrevStep = () => {
    if (step === 'select-posts') {
      setStep('select-images');
    } else if (step === 'pair-content') {
      setStep('select-posts');
    } else if (step === 'schedule') {
      setStep('pair-content');
    }
  };

  const canProceed = () => {
    if (step === 'select-images') return selectedImages.length > 0;
    if (step === 'select-posts') return selectedPosts.length > 0;
    if (step === 'pair-content') return pairs.every(pair => pair.image && pair.post);
    if (step === 'schedule') return pairs.every(pair => pair.scheduledDate && pair.scheduledTime);
    return false;
  };

  const handleFinalSchedule = () => {
    setScheduledPairs([...pairs]);
    setShowConfirmation(true);
    if (onScheduleComplete) {
      onScheduleComplete(pairs);
    }
  };

  const handleConfirmationClose = () => {
    setShowConfirmation(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-xl w-full max-w-6xl m-4 flex flex-col max-h-[90vh]">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-4">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Bulk Calendar Scheduler
            </h2>
            <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
              <span className={step === 'select-images' ? 'text-indigo-600 dark:text-indigo-400 font-medium' : ''}>
                1. Images
              </span>
              <span>→</span>
              <span className={step === 'select-posts' ? 'text-indigo-600 dark:text-indigo-400 font-medium' : ''}>
                2. Posts
              </span>
              <span>→</span>
              <span className={step === 'pair-content' ? 'text-indigo-600 dark:text-indigo-400 font-medium' : ''}>
                3. Pair
              </span>
              <span>→</span>
              <span className={step === 'schedule' ? 'text-indigo-600 dark:text-indigo-400 font-medium' : ''}>
                4. Schedule
              </span>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <XIcon className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-200 dark:scrollbar-thumb-gray-700">
          {showConfirmation ? (
            <ConfirmationStep
              scheduledPairs={scheduledPairs}
              onClose={handleConfirmationClose}
            />
          ) : (
            <>
              {step === 'select-images' && (
                <ImageSelectionStep
                  images={filteredImages}
                  selectedImages={selectedImages}
                  onImageSelect={handleImageSelect}
                  filter={imageFilter}
                  onFilterChange={setImageFilter}
                  isLoading={isLoading}
                />
              )}

              {step === 'select-posts' && (
                <PostSelectionStep
                  posts={filteredPosts}
                  allPosts={availablePosts}
                  selectedPosts={selectedPosts}
                  onPostSelect={handlePostSelect}
                  selectedImages={selectedImages}
                  postFilter={postFilter}
                  setPostFilter={setPostFilter}
                  uniqueCharacters={uniqueCharacters}
                />
              )}

              {step === 'pair-content' && (
                <PairingStep
                  pairs={pairs}
                  setPairs={setPairs}
                  selectedImages={selectedImages}
                  selectedPosts={selectedPosts}
                />
              )}

              {step === 'schedule' && (
                <SchedulingStep
                  pairs={pairs}
                  setPairs={setPairs}
                  currentDate={currentDate}
                  timeInterval={timeInterval}
                  setTimeInterval={setTimeInterval}
                />
              )}
            </>
          )}
        </div>

        {/* Footer */}
        {!showConfirmation && (
          <div className="flex justify-between items-center p-6 border-t border-gray-200 dark:border-gray-700">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {step === 'select-images' && `${selectedImages.length}/10 images selected`}
              {step === 'select-posts' && `${selectedPosts.length}/${Math.min(selectedImages.length, 10)} posts selected`}
              {step === 'pair-content' && `${pairs.filter(p => p.image && p.post).length}/${pairs.length} pairs completed`}
              {step === 'schedule' && `${pairs.filter(p => p.scheduledDate && p.scheduledTime).length}/${pairs.length} scheduled`}
            </div>

            <div className="flex space-x-3">
              {step !== 'select-images' && (
                <button
                  onClick={handlePrevStep}
                  className="px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100"
                >
                  <ChevronLeft className="w-4 h-4 inline mr-1" />
                  Back
                </button>
              )}

              <button
                onClick={step === 'schedule' ? handleFinalSchedule : handleNextStep}
                disabled={!canProceed()}
                className="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {step === 'schedule' ? 'Create Schedule' : 'Next'}
                {step !== 'schedule' && <ChevronRight className="w-4 h-4 inline ml-1" />}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Image Selection Step Component
function ImageSelectionStep({
  images,
  selectedImages,
  onImageSelect,
  filter,
  onFilterChange,
  isLoading
}: {
  images: Photo[];
  selectedImages: Photo[];
  onImageSelect: (photo: Photo) => void;
  filter: 'all' | 'uploaded' | 'generated';
  onFilterChange: (filter: 'all' | 'uploaded' | 'generated') => void;
  isLoading: boolean;
}) {
  return (
    <div className="p-6 h-full flex flex-col">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Select Images from Portfolio
        </h3>

        {/* Filter buttons */}
        <div className="flex items-center space-x-2">
          <Filter className="w-4 h-4 text-gray-500" />
          <div className="flex rounded-lg border border-gray-200 dark:border-gray-700">
            <button
              onClick={() => onFilterChange('all')}
              className={`px-3 py-1 text-sm rounded-l-lg ${
                filter === 'all'
                  ? 'bg-indigo-600 text-white'
                  : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
              }`}
            >
              All
            </button>
            <button
              onClick={() => onFilterChange('uploaded')}
              className={`px-3 py-1 text-sm border-l border-gray-200 dark:border-gray-700 ${
                filter === 'uploaded'
                  ? 'bg-indigo-600 text-white'
                  : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
              }`}
            >
              <Upload className="w-3 h-3 inline mr-1" />
              Uploaded
            </button>
            <button
              onClick={() => onFilterChange('generated')}
              className={`px-3 py-1 text-sm rounded-r-lg border-l border-gray-200 dark:border-gray-700 ${
                filter === 'generated'
                  ? 'bg-indigo-600 text-white'
                  : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
              }`}
            >
              <Sparkles className="w-3 h-3 inline mr-1" />
              AI Generated
            </button>
          </div>
        </div>
      </div>

      {isLoading ? (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <Loader2 className="w-8 h-8 animate-spin text-indigo-600 mx-auto mb-2" />
            <p className="text-gray-500 dark:text-gray-400">Loading portfolio images...</p>
          </div>
        </div>
      ) : (
        <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-200 dark:scrollbar-thumb-gray-700">
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
            {images.map(photo => {
              const isSelected = selectedImages.find(img => img.id === photo.id);
              const canSelect = selectedImages.length < 10 || isSelected;

              return (
                <div
                  key={photo.id}
                  onClick={() => canSelect && onImageSelect(photo)}
                  className={`relative aspect-square rounded-lg overflow-hidden cursor-pointer border-2 transition-all ${
                    isSelected
                      ? 'border-indigo-500 ring-2 ring-indigo-200 dark:ring-indigo-800'
                      : canSelect
                      ? 'border-gray-200 dark:border-gray-700 hover:border-indigo-300 dark:hover:border-indigo-600'
                      : 'border-gray-200 dark:border-gray-700 opacity-50 cursor-not-allowed'
                  }`}
                >
                  <img
                    src={photo.thumbnailUrl}
                    alt={photo.title}
                    className="w-full h-full object-cover"
                  />

                  {/* Selection indicator with counter */}
                  {isSelected && (
                    <div className="absolute top-2 right-2 w-6 h-6 bg-indigo-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">
                        {selectedImages.findIndex(img => img.id === photo.id) + 1}
                      </span>
                    </div>
                  )}

                  {/* Image type indicator */}
                  <div className="absolute top-2 left-2">
                    {photo.isGenerated ? (
                      <div className="w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center">
                        <Sparkles className="w-3 h-3 text-white" />
                      </div>
                    ) : (
                      <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                        <Upload className="w-3 h-3 text-white" />
                      </div>
                    )}
                  </div>

                  {/* Image title */}
                  <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white p-2 text-xs">
                    <p className="truncate">{photo.title}</p>
                  </div>
                </div>
              );
            })}
          </div>

          {images.length === 0 && (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 dark:text-gray-400">No images found</p>
                <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
                  Try changing the filter or add images to your portfolio
                </p>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

// Post Selection Step Component
function PostSelectionStep({
  posts,
  allPosts,
  selectedPosts,
  onPostSelect,
  selectedImages,
  postFilter,
  setPostFilter,
  uniqueCharacters
}: {
  posts: GeneratedPost[];
  allPosts: GeneratedPost[];
  selectedPosts: GeneratedPost[];
  onPostSelect: (post: GeneratedPost) => void;
  selectedImages: Photo[];
  postFilter: {
    character: string;
    dateRange: string;
    searchText: string;
  };
  setPostFilter: (filter: any) => void;
  uniqueCharacters: string[];
}) {
  const maxPosts = Math.min(selectedImages.length, 10);
  return (
    <div className="p-6 h-full flex flex-col">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Select Posts from Bulk Generations
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Select up to {maxPosts} posts (limited by {selectedImages.length} selected images)
            </p>
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {posts.length} of {allPosts.length} posts shown
          </div>
        </div>

        {/* Filter Controls - Matching BulkGenerations style */}
        <div className="space-y-4 mb-6">
          {/* Search Filter */}
          <div>
            <input
              type="text"
              placeholder="Search posts and characters..."
              value={postFilter.searchText}
              onChange={(e) => setPostFilter({...postFilter, searchText: e.target.value})}
              className="w-full px-4 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
            />
          </div>

          {/* Filter Buttons - Matching BulkGenerations layout */}
          <div className="flex items-center space-x-2 overflow-x-auto pb-2">
            <button
              onClick={() => setPostFilter({...postFilter, character: 'all'})}
              className={`px-3 py-1.5 rounded-md text-sm font-medium flex items-center whitespace-nowrap ${
                postFilter.character === 'all'
                  ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300'
                  : 'bg-white text-gray-700 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              <MessageSquare className="w-4 h-4 inline-block mr-1.5" />
              <span>All Posts</span>
            </button>

            <button
              onClick={() => setPostFilter({...postFilter, character: 'character'})}
              className={`px-3 py-1.5 rounded-md text-sm font-medium flex items-center whitespace-nowrap ${
                postFilter.character === 'character'
                  ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300'
                  : 'bg-white text-gray-700 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              <User className="w-4 h-4 inline-block mr-1.5" />
              <span>By Character</span>
            </button>

            <button
              onClick={() => setPostFilter({...postFilter, character: 'general'})}
              className={`px-3 py-1.5 rounded-md text-sm font-medium flex items-center whitespace-nowrap ${
                postFilter.character === 'general'
                  ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300'
                  : 'bg-white text-gray-700 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              <FileText className="w-4 h-4 inline-block mr-1.5" />
              <span>General Posts</span>
            </button>

            {/* Specific Character Filters */}
            {uniqueCharacters.map(character => (
              <button
                key={character}
                onClick={() => setPostFilter({...postFilter, character: character})}
                className={`px-3 py-1.5 rounded-md text-sm font-medium flex items-center whitespace-nowrap ${
                  postFilter.character === character
                    ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300'
                    : 'bg-white text-gray-700 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                }`}
              >
                <User className="w-4 h-4 inline-block mr-1.5" />
                <span>{character}</span>
              </button>
            ))}
          </div>

          {/* Date Filter Buttons */}
          <div className="flex items-center space-x-2 overflow-x-auto pb-2">
            <span className="text-sm text-gray-500 dark:text-gray-400 mr-2">Filter by date:</span>
            <button
              onClick={() => setPostFilter({...postFilter, dateRange: 'all'})}
              className={`px-3 py-1 rounded-md text-xs font-medium flex items-center whitespace-nowrap ${
                postFilter.dateRange === 'all'
                  ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300'
                  : 'bg-white text-gray-500 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600'
              }`}
            >
              <Calendar className="w-3.5 h-3.5 inline-block mr-1.5" />
              <span>All Time</span>
            </button>

            <button
              onClick={() => setPostFilter({...postFilter, dateRange: 'today'})}
              className={`px-3 py-1 rounded-md text-xs font-medium flex items-center whitespace-nowrap ${
                postFilter.dateRange === 'today'
                  ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300'
                  : 'bg-white text-gray-500 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600'
              }`}
            >
              <Calendar className="w-3.5 h-3.5 inline-block mr-1.5" />
              <span>Today</span>
            </button>

            <button
              onClick={() => setPostFilter({...postFilter, dateRange: 'week'})}
              className={`px-3 py-1 rounded-md text-xs font-medium flex items-center whitespace-nowrap ${
                postFilter.dateRange === 'week'
                  ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300'
                  : 'bg-white text-gray-500 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600'
              }`}
            >
              <Calendar className="w-3.5 h-3.5 inline-block mr-1.5" />
              <span>This Week</span>
            </button>

            <button
              onClick={() => setPostFilter({...postFilter, dateRange: 'month'})}
              className={`px-3 py-1 rounded-md text-xs font-medium flex items-center whitespace-nowrap ${
                postFilter.dateRange === 'month'
                  ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300'
                  : 'bg-white text-gray-500 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600'
              }`}
            >
              <Calendar className="w-3.5 h-3.5 inline-block mr-1.5" />
              <span>This Month</span>
            </button>
          </div>

          {/* Clear Filters Button */}
          {(postFilter.character !== 'all' || postFilter.dateRange !== 'all' || postFilter.searchText.trim()) && (
            <div className="flex justify-end">
              <button
                onClick={() => setPostFilter({
                  character: 'all',
                  dateRange: 'all',
                  searchText: ''
                })}
                className="text-sm text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300"
              >
                Clear all filters
              </button>
            </div>
          )}
        </div>
      </div>

      <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-200 dark:scrollbar-thumb-gray-700">
        {selectedImages.length === 0 ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400">No images selected</p>
              <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
                Go back and select some images first to enable post selection
              </p>
            </div>
          </div>
        ) : posts.length === 0 ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400">No bulk generated posts available</p>
              <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
                Generate some posts first using the bulk generation feature
              </p>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {posts.map(post => {
              const isSelected = selectedPosts.find(p => p.id === post.id);
              const canSelect = selectedPosts.length < maxPosts || isSelected;

              return (
                <div
                  key={post.id}
                  onClick={() => canSelect && onPostSelect(post)}
                  className={`relative p-4 rounded-lg border-2 cursor-pointer transition-all ${
                    isSelected
                      ? 'border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20'
                      : canSelect
                      ? 'border-gray-200 dark:border-gray-700 hover:border-indigo-300 dark:hover:border-indigo-600 bg-white dark:bg-gray-800'
                      : 'border-gray-200 dark:border-gray-700 opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800'
                  }`}
                  title={!canSelect && !isSelected ? `Maximum ${maxPosts} posts allowed (limited by selected images)` : undefined}
                >
                  {/* Selection indicator with counter */}
                  {isSelected && (
                    <div className="absolute top-3 right-3 w-6 h-6 bg-indigo-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">
                        {selectedPosts.findIndex(p => p.id === post.id) + 1}
                      </span>
                    </div>
                  )}

                  {/* Post content */}
                  <div className="pr-8">
                    <div className="text-sm text-gray-800 dark:text-gray-200 line-clamp-6 mb-3">
                      {post.content}
                    </div>

                    {/* Post metadata */}
                    <div className="space-y-2">
                      {/* Character info if available */}
                      {post.characterInfo && (
                        <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                          <div className="w-4 h-4 bg-indigo-100 dark:bg-indigo-900/30 rounded flex items-center justify-center">
                            <MessageSquare className="w-2.5 h-2.5 text-indigo-600 dark:text-indigo-400" />
                          </div>
                          <span>{post.characterInfo.name}</span>
                        </div>
                      )}


                    </div>

                    {/* Timestamp */}
                    <div className="text-xs text-gray-400 dark:text-gray-500 mt-3 pt-2 border-t border-gray-200 dark:border-gray-700">
                      Generated {post.timestamp.toLocaleDateString()}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}

// Pairing Step Component
function PairingStep({
  pairs,
  setPairs,
  selectedImages,
  selectedPosts
}: {
  pairs: ImagePostPair[];
  setPairs: (pairs: ImagePostPair[]) => void;
  selectedImages: Photo[];
  selectedPosts: GeneratedPost[];
}) {
  const updatePair = (pairId: string, field: 'image' | 'post', value: Photo | GeneratedPost | null) => {
    setPairs(pairs.map(pair =>
      pair.id === pairId
        ? { ...pair, [field]: value }
        : pair
    ));
  };

  const addPair = () => {
    const newPair: ImagePostPair = {
      id: `pair-${Date.now()}`,
      image: null,
      post: null,
    };
    setPairs([...pairs, newPair]);
  };

  const removePair = (pairId: string) => {
    setPairs(pairs.filter(pair => pair.id !== pairId));
  };

  return (
    <div className="p-6 h-full flex flex-col">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Pair Images with Posts
        </h3>
        <button
          onClick={addPair}
          className="flex items-center space-x-2 px-3 py-2 text-sm bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
        >
          <Plus className="w-4 h-4" />
          <span>Add Pair</span>
        </button>
      </div>

      <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-200 dark:scrollbar-thumb-gray-700">
        <div className="space-y-4">
          {pairs.map((pair, index) => (
            <div
              key={pair.id}
              className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4"
            >
              <div className="flex items-center justify-between mb-4">
                <h4 className="font-medium text-gray-900 dark:text-white">
                  Pair {index + 1}
                </h4>
                {pairs.length > 1 && (
                  <button
                    onClick={() => removePair(pair.id)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <XIcon className="w-4 h-4" />
                  </button>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Image Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Select Image
                  </label>
                  <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                    {pair.image ? (
                      <div className="flex items-center space-x-3">
                        <img
                          src={pair.image.thumbnailUrl}
                          alt={pair.image.title}
                          className="w-12 h-12 object-cover rounded"
                        />
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {pair.image.title}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {pair.image.isGenerated ? 'AI Generated' : 'Uploaded'}
                          </p>
                        </div>
                        <button
                          onClick={() => updatePair(pair.id, 'image', null)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <XIcon className="w-4 h-4" />
                        </button>
                      </div>
                    ) : (
                      <select
                        onChange={(e) => {
                          const image = selectedImages.find(img => img.id === e.target.value);
                          updatePair(pair.id, 'image', image || null);
                        }}
                        className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      >
                        <option value="">Choose an image...</option>
                        {selectedImages.map(image => (
                          <option key={image.id} value={image.id}>
                            {image.title} ({image.isGenerated ? 'AI' : 'Uploaded'})
                          </option>
                        ))}
                      </select>
                    )}
                  </div>
                </div>

                {/* Post Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Select Post
                  </label>
                  <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                    {pair.post ? (
                      <div className="flex items-start space-x-3">
                        <div className="flex-1">
                          <p className="text-sm text-gray-900 dark:text-white line-clamp-3">
                            {pair.post.content}
                          </p>
                          {pair.post.characterInfo && (
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              by {pair.post.characterInfo.name}
                            </p>
                          )}
                        </div>
                        <button
                          onClick={() => updatePair(pair.id, 'post', null)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <XIcon className="w-4 h-4" />
                        </button>
                      </div>
                    ) : (
                      <select
                        onChange={(e) => {
                          const post = selectedPosts.find(p => p.id === e.target.value);
                          updatePair(pair.id, 'post', post || null);
                        }}
                        className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      >
                        <option value="">Choose a post...</option>
                        {selectedPosts.map(post => (
                          <option key={post.id} value={post.id}>
                            {post.content.substring(0, 50)}...
                          </option>
                        ))}
                      </select>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Scheduling Step Component
function SchedulingStep({
  pairs,
  setPairs,
  currentDate,
  timeInterval,
  setTimeInterval
}: {
  pairs: ImagePostPair[];
  setPairs: (pairs: ImagePostPair[]) => void;
  currentDate: Date;
  timeInterval: 15 | 30 | 60;
  setTimeInterval: (interval: 15 | 30 | 60) => void;
}) {
  const updatePairSchedule = (pairId: string, date: Date, time: string) => {
    setPairs(pairs.map(pair =>
      pair.id === pairId
        ? { ...pair, scheduledDate: date, scheduledTime: time }
        : pair
    ));
  };

  const formatDate = (date: Date) => {
    // Use local date formatting to avoid timezone issues
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const generateTimeSlots = () => {
    const slots = [];
    // Generate slots from 12:00 AM to 11:59 PM based on selected interval
    for (let hour = 0; hour <= 23; hour++) {
      for (let minute = 0; minute < 60; minute += timeInterval) {
        const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        const displayTime = new Date(`2000-01-01T${time}`).toLocaleTimeString([], {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        });
        slots.push({ value: time, label: displayTime });
      }
    }
    return slots;
  };

  const timeSlots = generateTimeSlots();

  return (
    <div className="p-6 h-full flex flex-col">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Schedule Your Content
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Set the date and time for each image/post pair
            </p>
          </div>

          {/* Time Interval Selector */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600 dark:text-gray-400">Time intervals:</span>
            <div className="flex rounded-lg border border-gray-200 dark:border-gray-700">
              <button
                onClick={() => setTimeInterval(15)}
                className={`px-3 py-1 text-sm rounded-l-lg ${
                  timeInterval === 15
                    ? 'bg-indigo-600 text-white'
                    : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
              >
                15min
              </button>
              <button
                onClick={() => setTimeInterval(30)}
                className={`px-3 py-1 text-sm border-l border-gray-200 dark:border-gray-700 ${
                  timeInterval === 30
                    ? 'bg-indigo-600 text-white'
                    : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
              >
                30min
              </button>
              <button
                onClick={() => setTimeInterval(60)}
                className={`px-3 py-1 text-sm rounded-r-lg border-l border-gray-200 dark:border-gray-700 ${
                  timeInterval === 60
                    ? 'bg-indigo-600 text-white'
                    : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
              >
                1hr
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-200 dark:scrollbar-thumb-gray-700">
        <div className="space-y-6">
          {pairs.map((pair, index) => (
            <div
              key={pair.id}
              className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4"
            >
              <div className="flex items-center justify-between mb-4">
                <h4 className="font-medium text-gray-900 dark:text-white">
                  Pair {index + 1}
                </h4>
                <div className="flex items-center space-x-2">
                  {pair.scheduledDate && pair.scheduledTime && (
                    <div className="flex items-center space-x-1 text-green-600 dark:text-green-400">
                      <Check className="w-4 h-4" />
                      <span className="text-sm">Scheduled</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Content Preview */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  {pair.image && (
                    <>
                      <img
                        src={pair.image.thumbnailUrl}
                        alt={pair.image.title}
                        className="w-12 h-12 object-cover rounded"
                      />
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          {pair.image.title}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {pair.image.isGenerated ? 'AI Generated' : 'Uploaded'}
                        </p>
                      </div>
                    </>
                  )}
                </div>

                <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  {pair.post && (
                    <p className="text-sm text-gray-900 dark:text-white line-clamp-3">
                      {pair.post.content}
                    </p>
                  )}
                </div>
              </div>

              {/* Scheduling Controls */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <Calendar className="w-4 h-4 inline mr-1" />
                    Date
                  </label>
                  <input
                    type="date"
                    value={pair.scheduledDate ? formatDate(pair.scheduledDate) : formatDate(currentDate)}
                    min={formatDate(new Date())} // Allow today's date
                    onChange={(e) => {
                      // Parse date in local timezone to avoid timezone shifts
                      const [year, month, day] = e.target.value.split('-').map(Number);
                      const date = new Date(year, month - 1, day); // month is 0-indexed
                      updatePairSchedule(pair.id, date, pair.scheduledTime || '09:00');
                    }}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <Clock className="w-4 h-4 inline mr-1" />
                    Time
                  </label>
                  <select
                    value={pair.scheduledTime || '09:00'}
                    onChange={(e) => {
                      const date = pair.scheduledDate || currentDate;
                      updatePairSchedule(pair.id, date, e.target.value);
                    }}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    {timeSlots.map(slot => (
                      <option key={slot.value} value={slot.value}>
                        {slot.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Confirmation Step Component
function ConfirmationStep({
  scheduledPairs,
  onClose
}: {
  scheduledPairs: ImagePostPair[];
  onClose: () => void;
}) {
  return (
    <div className="p-6 h-full flex flex-col">
      <div className="text-center mb-8">
        <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
          <Check className="w-8 h-8 text-green-600 dark:text-green-400" />
        </div>
        <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-2">
          Schedule Created Successfully!
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          {scheduledPairs.length} posts have been scheduled and added to your calendar.
        </p>
      </div>

      <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-200 dark:scrollbar-thumb-gray-700">
        <div className="space-y-4">
          {scheduledPairs.map((pair, index) => (
            <div
              key={pair.id}
              className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4"
            >
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-gray-900 dark:text-white">
                  Post {index + 1}
                </h4>
                <div className="flex items-center space-x-2 text-green-600 dark:text-green-400">
                  <Check className="w-4 h-4" />
                  <span className="text-sm font-medium">Scheduled</span>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Image Preview */}
                <div className="flex items-center space-x-3">
                  {pair.image && (
                    <>
                      <img
                        src={pair.image.thumbnailUrl}
                        alt={pair.image.title}
                        className="w-12 h-12 object-cover rounded"
                      />
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          {pair.image.title}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {pair.image.isGenerated ? 'AI Generated' : 'Uploaded'}
                        </p>
                      </div>
                    </>
                  )}
                </div>

                {/* Post Content */}
                <div className="md:col-span-1">
                  {pair.post && (
                    <div>
                      <p className="text-sm text-gray-900 dark:text-white line-clamp-3">
                        {pair.post.content}
                      </p>
                      {pair.post.characterInfo && (
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          by {pair.post.characterInfo.name}
                        </p>
                      )}
                    </div>
                  )}
                </div>

                {/* Schedule Info */}
                <div className="flex items-center space-x-4 text-sm">
                  <div className="flex items-center space-x-1 text-gray-600 dark:text-gray-400">
                    <Calendar className="w-4 h-4" />
                    <span>
                      {pair.scheduledDate?.toLocaleDateString()}
                    </span>
                  </div>
                  <div className="flex items-center space-x-1 text-gray-600 dark:text-gray-400">
                    <Clock className="w-4 h-4" />
                    <span>
                      {pair.scheduledTime && new Date(`2000-01-01T${pair.scheduledTime}`).toLocaleTimeString([], {
                        hour: 'numeric',
                        minute: '2-digit',
                        hour12: true
                      })}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Confirmation Footer */}
      <div className="flex justify-center pt-6 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={onClose}
          className="px-8 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 font-medium"
        >
          Done
        </button>
      </div>
    </div>
  );
}
