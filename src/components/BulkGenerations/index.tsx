import React, { useState, useMemo, useEffect } from 'react';
import {
  FileText, Bot, Plus, Copy, ChevronDown, ChevronUp,
  Save, Calendar, X as XIcon, CheckCircle, Loader2, Image,
  Hash, User, ArrowLeft, Trash2, Edit2 as Edit, Filter, FileImage, MessageSquare,
  ListOrdered, Minus, Sparkles, MessageCircle, SearchX, AlertTriangle,
  CheckSquare, Square, Trash
} from 'lucide-react';
import { PageLayout } from '../common/PageLayout';
import { useNavigate } from 'react-router-dom';
import { useFalTextGeneration } from "../../hooks/useFalTextGeneration";

// Types for our post generations
interface GeneratedPost {
  id: string;
  content: string;
  isSelected: boolean;
  timestamp: Date;
  characterInfo?: {
    id: string;
    name: string;
    avatar: string;
  };
}

// Sample character data
interface Character {
  id: string;
  name: string;
  avatar: string;
  description: string;
}

const sampleCharacters: Character[] = [
  {
    id: 'char1',
    name: 'StarBro Official',
    avatar: '/media/GmgegOpaEAIyUoL.jpg',
    description: 'StarBro overlooking a scenic valley at sunset, wearing blue and gold suit with star head and sunglasses'
  },
  {
    id: 'char2',
    name: 'StarBro Royal',
    avatar: '/media/GnJdhT1bcAAv-QA.jpeg',
    description: 'StarBro sitting on golden throne in royal chamber, with ornate gold armor and XRP insignia'
  },
  {
    id: 'char3',
    name: 'StarBro Prosperity',
    avatar: '/media/GnJrJvmaoAAK_Od.jpeg',
    description: 'StarBro standing on golden XRP coins above clouds at sunrise with arms crossed, radiating confidence'
  },
  {
    id: 'char4',
    name: 'StarBro Finance',
    avatar: '/media/GnKKhAmbsAEl7E4.jpeg', 
    description: 'StarBro in vault surrounded by gold bars and XRP tokens, with market chart background and cape flowing'
  }
];

// Sample prompts for generating content
const samplePrompts = [
  {
    id: 'p1',
    title: 'Daily Update',
    description: 'Share updates about your project with your audience',
    content: 'Create a daily update post about [project name]. Include 2-3 latest developments. Use a conversational tone. End with an engaging question for followers. Include relevant hashtags.',
  },
  {
    id: 'p2',
    title: 'Market Insights',
    description: 'Share cryptocurrency market insights and analysis',
    content: 'Generate a market analysis post for [coin name]. Include current price action, key resistance/support levels, and one prediction. Use a professional tone with one emoji. End with a disclaimer.',
  },
  {
    id: 'p3',
    title: 'Community Engagement',
    description: 'Post that encourages community participation',
    content: 'Create an engaging community post for [community name]. Ask a question about [topic]. Include a call to action encouraging replies. Keep it short and conversational with 1-2 emojis.',
  },
  {
    id: 'p4',
    title: 'Feature Highlight',
    description: 'Highlight a specific feature of your product/project',
    content: 'Write a post highlighting the [feature name] of [project]. Explain how it benefits users in 2-3 sentences. Include a simple use case. End with a call to action to try the feature. Use emojis for visual appeal.',
  },
  {
    id: 'p5',
    title: 'Event Announcement',
    description: 'Announce an upcoming event or AMA',
    content: 'Create an announcement for [event name] happening on [date]. Include time, platform, and main participants. Highlight 1-2 key topics to be discussed. End with instructions on how to join. Use an excited tone.',
  }
];

// AI Generation Modal Component
function GenerationModal({ isOpen, onClose, onGenerate, currentPostCount }: {
  isOpen: boolean;
  onClose: () => void;
  onGenerate: (posts: GeneratedPost[]) => void;
  currentPostCount: number;
}) {
  const navigate = useNavigate();
  const [prompt, setPrompt] = useState('');
  const [numPosts, setNumPosts] = useState(5);
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedCharacter, setSelectedCharacter] = useState<string | null>(null);
  const [hashtags, setHashtags] = useState<string[]>([]);
  const [newHashtag, setNewHashtag] = useState('');
  
  // Use our FAL text generation hook
  const { 
    generateTexts, 
    results: generatedTexts, 
    loading: textsLoading, 
    reset: resetTexts, 
    edgeFunctionMissing 
  } = useFalTextGeneration();

  const characters = sampleCharacters;

  // Handle hashtag addition
  const handleAddHashtag = () => {
    if (newHashtag && !hashtags.includes(newHashtag)) {
      setHashtags([...hashtags, newHashtag]);
      setNewHashtag('');
    }
  };
  
  // Handle hashtag removal
  const handleRemoveHashtag = (hashtag: string) => {
    setHashtags(hashtags.filter(tag => tag !== hashtag));
  };
  
  // Handle new hashtag input keypress
  const handleHashtagKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && newHashtag) {
      e.preventDefault();
      handleAddHashtag();
    }
  };
  
  // Handle character selection
  const handleCharacterSelect = (id: string) => {
    setSelectedCharacter(id === selectedCharacter ? null : id);
  };

  // Handle generation
  const handleGenerate = async () => {
    // Clear previous results
    resetTexts();

    if (!prompt.trim()) {
      alert("Please enter a prompt");
      return;
    }

    // Check if generation would exceed post limit
    if (currentPostCount + numPosts > 250) {
      alert(`Cannot generate ${numPosts} posts. You would exceed the maximum limit of 250 posts. You currently have ${currentPostCount} posts.`);
      return;
    }
    
    // Gather keywords from hashtags
    const keywordsList = hashtags
      .filter(tag => tag.trim().length > 0)
      .map(tag => tag.startsWith('#') ? tag.substring(1) : tag);
    
    try {
      // Generate the specified number of posts using FAL
      const results = await generateTexts({
        prompt,
        character: selectedCharacter 
          ? characters.find(char => char.id === selectedCharacter)?.name 
          : undefined,
        keywords: keywordsList,
        count: numPosts
      });
      
      if (results && results.length > 0) {
        // Map the generated texts to Post objects
        const generatedPosts: GeneratedPost[] = results.map(text => ({
          id: text.id,
          content: text.content,
          isSelected: false, // Default to unselected
          timestamp: text.timestamp,
          characterInfo: selectedCharacter
            ? characters.find(char => char.id === selectedCharacter)
            : undefined,
        }));
        
        // Set the generated posts
        onGenerate(generatedPosts);
        
        // Close the modal
        onClose();
      }
    } catch (error) {
      console.error("Error generating posts:", error);
      alert("Error generating posts. Please try again.");
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-2xl mx-auto overflow-hidden">
        <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">Generate Posts</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            <XIcon className="w-5 h-5" />
          </button>
        </div>
        
        {/* Character Selection */}
        <div className="p-5 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
            <User className="w-4 h-4 mr-2" />
            Generate as Character <span className="text-xs ml-2 font-normal text-gray-500">(Optional)</span>
          </h3>
          <div className="grid grid-cols-4 gap-4 mt-3">
            {characters.map((character) => (
              <div 
                key={character.id} 
                className={`flex flex-col items-center cursor-pointer transition-all duration-200 ${
                  character.id === selectedCharacter
                    ? 'scale-105'
                    : 'hover:scale-105'
                }`}
                onClick={() => handleCharacterSelect(character.id)}
              >
                <div className={`w-16 h-16 rounded-lg overflow-hidden border-2 ${
                  character.id === selectedCharacter
                    ? 'border-indigo-500 dark:border-indigo-400'
                    : 'border-transparent'
                }`}>
                  <img 
                    src={character.avatar} 
                    alt={character.name} 
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className={`text-xs font-medium transition-colors truncate px-1 ${
                  character.id === selectedCharacter
                    ? 'text-indigo-600 dark:text-indigo-400'
                    : 'text-gray-600 dark:text-gray-400'
                }`}>
                  {character.name}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Content that expands if character is selected */}
        <div className="p-6 space-y-6">
          {/* Number of posts to generate */}
          <div>
            <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
              <ListOrdered className="w-4 h-4 mr-2" />
              Number of Posts to Generate
            </h3>
            <div className="flex items-center justify-center space-x-4">
              <button
                onClick={() => setNumPosts(Math.max(1, numPosts - 1))}
                className="p-2 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                disabled={numPosts <= 1}
              >
                <Minus className="w-5 h-5" />
              </button>
              
              <div className="w-16 text-center">
                <input
                  type="number"
                  min="1"
                  max="10"
                  value={numPosts}
                  onChange={(e) => setNumPosts(Math.min(10, Math.max(1, parseInt(e.target.value) || 1)))}
                  className="w-full text-center border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white py-2"
                />
              </div>
              
              <button
                onClick={() => setNumPosts(Math.min(10, numPosts + 1))}
                className="p-2 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                disabled={numPosts >= 10}
              >
                <Plus className="w-5 h-5" />
              </button>
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
              This will generate {numPosts} post{numPosts > 1 ? 's' : ''} based on your settings.
            </p>
          </div>

          {/* Hashtags Section */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
              <Hash className="w-4 h-4 mr-2" />
              Hashtags
            </label>
            <div className="flex items-center space-x-2 mb-2">
              <input
                type="text"
                value={newHashtag}
                onChange={(e) => setNewHashtag(e.target.value)}
                onKeyPress={handleHashtagKeyPress}
                placeholder="Add hashtag"
                className="flex-grow px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700"
              />
              <button
                onClick={handleAddHashtag}
                disabled={!newHashtag}
                className={`px-3 py-2 rounded-lg ${
                  !newHashtag 
                    ? 'bg-gray-300 dark:bg-gray-600 cursor-not-allowed' 
                    : 'bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-500 dark:hover:bg-indigo-600'
                } text-white`}
              >
                Add
              </button>
            </div>
            
            <div className="flex flex-wrap gap-2">
              {hashtags.map(hashtag => (
                <div key={hashtag} className="flex items-center bg-indigo-50 dark:bg-indigo-900/30 rounded-full pl-3 pr-1 py-1">
                  <span className="text-sm text-indigo-800 dark:text-indigo-300">{hashtag}</span>
                  <button 
                    onClick={() => handleRemoveHashtag(hashtag)}
                    className="ml-1 rounded-full p-1 hover:bg-indigo-100 dark:hover:bg-indigo-800"
                  >
                    <XIcon className="w-3 h-3 text-indigo-700 dark:text-indigo-300" />
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* Prompt Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
              <MessageCircle className="w-4 h-4 mr-2" />
              Prompt
            </label>
            <textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="What kind of post do you want to generate? Be specific about the tone, topic, and style..."
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white resize-none"
            ></textarea>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
              Enter your prompt to generate content. Be specific about the style, tone, and content you want.
            </p>
          </div>

          {/* Post limit warning */}
          {currentPostCount + numPosts > 200 && (
            <div className="bg-amber-50 dark:bg-amber-900/30 border border-amber-200 dark:border-amber-700 rounded-lg p-3 mb-4">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="w-4 h-4 text-amber-600 dark:text-amber-400" />
                <div className="text-sm">
                  <span className="font-medium text-amber-800 dark:text-amber-200">
                    {currentPostCount + numPosts > 250
                      ? 'Cannot generate: Would exceed post limit'
                      : 'Warning: Approaching post limit'
                    }
                  </span>
                  <p className="text-amber-700 dark:text-amber-300 mt-1">
                    You currently have {currentPostCount} posts.
                    {currentPostCount + numPosts > 250
                      ? ` Generating ${numPosts} more would exceed the 250 post limit.`
                      : ` After generating ${numPosts} posts, you'll have ${currentPostCount + numPosts} of 250 posts.`
                    }
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4 mt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              Cancel
            </button>
            <button
              disabled={textsLoading || currentPostCount + numPosts > 250}
              onClick={handleGenerate}
              className="flex items-center justify-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white py-2.5 px-6 rounded-lg font-medium shadow-sm disabled:opacity-70 disabled:pointer-events-none transition-all"
            >
              {textsLoading ? (
                <>
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  <span>Generating...</span>
                </>
              ) : (
                <>
                  <Sparkles className="w-5 h-5 mr-2" />
                  <span>Generate {numPosts} Post{numPosts !== 1 ? 's' : ''}</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Write Post Modal Component
function WritePostModal({ isOpen, onClose, onSubmit }: { 
  isOpen: boolean; 
  onClose: () => void; 
  onSubmit: (post: GeneratedPost) => void;
}) {
  const [content, setContent] = useState('');
  const [hashtags, setHashtags] = useState('');
  const [selectedCharacter, setSelectedCharacter] = useState<string | null>(null);
  const characters = sampleCharacters;

  const handleSubmit = () => {
    if (!content.trim()) {
      alert('Please enter some content for your post');
      return;
    }

    // Format hashtags properly
    const formattedHashtags = hashtags
      .split(/[ ,]+/) // Split by spaces or commas
      .filter(tag => tag.trim() !== '') // Remove empty tags
      .map(tag => tag.startsWith('#') ? tag : `#${tag}`) // Add # if not present
      .join(' '); // Join with spaces
    
    // Combine content with hashtags if any exist
    const finalContent = formattedHashtags
      ? `${content.trim()} ${formattedHashtags}`
      : content.trim();

    const newPost: GeneratedPost = {
      id: Date.now().toString(),
      content: finalContent,
      isSelected: false,
      timestamp: new Date(),
      characterInfo: selectedCharacter 
        ? characters.find(char => char.id === selectedCharacter)
        : undefined
    };

    onSubmit(newPost);
    onClose();
    setContent('');
    setHashtags('');
    setSelectedCharacter(null);
  };

  const handleCharacterSelect = (id: string) => {
    setSelectedCharacter(prevId => prevId === id ? null : id);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-2xl mx-auto overflow-hidden">
        <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">Write a Post</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            <XIcon className="w-5 h-5" />
          </button>
        </div>
        
        {/* Character Selection (Optional) */}
        <div className="p-5 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
            <User className="w-4 h-4 mr-2" />
            Post as Character <span className="text-xs ml-2 font-normal text-gray-500">(Optional)</span>
          </h3>
          <div className="grid grid-cols-4 gap-4 mt-3">
            {characters.map((character) => (
              <div 
                key={character.id} 
                className={`flex flex-col items-center cursor-pointer transition-all duration-200 ${
                  character.id === selectedCharacter
                    ? 'scale-105'
                    : 'hover:scale-105'
                }`}
                onClick={() => handleCharacterSelect(character.id)}
              >
                <div className={`w-16 h-16 rounded-lg overflow-hidden border-2 ${
                  character.id === selectedCharacter
                    ? 'border-indigo-500 dark:border-indigo-400'
                    : 'border-transparent'
                }`}>
                  <img 
                    src={character.avatar} 
                    alt={character.name} 
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className={`text-xs font-medium transition-colors truncate px-1 ${
                  character.id === selectedCharacter
                    ? 'text-indigo-600 dark:text-indigo-400'
                    : 'text-gray-600 dark:text-gray-400'
                }`}>
                  {character.name}
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* Post Content */}
        <div className="p-5">
          <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
            <MessageSquare className="w-4 h-4 mr-2" />
            Write Your Post
          </h3>
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="What's on your mind?"
            className="w-full h-40 p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white resize-none"
          />
          <div className="text-xs text-gray-500 dark:text-gray-400 mt-2 text-right">
            {content.length} characters
          </div>
        </div>

        {/* Hashtags */}
        <div className="px-5 pb-5 pt-1">
          <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
            <Hash className="w-4 h-4 mr-2" />
            Add Hashtags <span className="text-xs ml-2 font-normal text-gray-500">(Optional)</span>
          </h3>
          <div className="relative">
            <input
              type="text"
              value={hashtags}
              onChange={(e) => setHashtags(e.target.value)}
              placeholder="Add hashtags separated by spaces (e.g., crypto xrp blockchain)"
              className="w-full p-3 pl-8 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 text-gray-900 dark:text-white"
            />
            <Hash className="w-4 h-4 text-gray-400 absolute left-2.5 top-3.5" />
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
            {hashtags.split(/[ ,]+/).filter(tag => tag.trim() !== '').length > 0 && (
              <div className="flex flex-wrap gap-1.5 mt-2">
                {hashtags.split(/[ ,]+/).filter(tag => tag.trim() !== '').map((tag, index) => (
                  <span key={index} className="bg-indigo-100 dark:bg-indigo-900/40 text-indigo-800 dark:text-indigo-300 px-2 py-0.5 rounded-full inline-flex items-center">
                    {tag.startsWith('#') ? tag : `#${tag}`}
                  </span>
                ))}
              </div>
            )}
          </div>
        </div>
        
        {/* Action Buttons */}
        <div className="px-5 py-4 bg-gray-50 dark:bg-gray-800/50 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg font-medium shadow-sm"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            className="flex items-center justify-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white py-2.5 px-6 rounded-lg font-medium shadow-sm disabled:opacity-70 disabled:pointer-events-none transition-all"
          >
            <FileText className="w-5 h-5 mr-2" />
            <span>Post</span>
          </button>
        </div>
      </div>
    </div>
  );
}

// Cleanup Modal Component
function CleanupModal({
  isOpen,
  onClose,
  posts,
  onDeletePosts
}: {
  isOpen: boolean;
  onClose: () => void;
  posts: GeneratedPost[];
  onDeletePosts: (postIds: string[]) => void;
}) {
  const [selectedCleanupPosts, setSelectedCleanupPosts] = useState<string[]>([]);

  // Sort posts by date (oldest first)
  const sortedPosts = [...posts].sort((a, b) =>
    new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
  );

  const handleTogglePost = (postId: string) => {
    setSelectedCleanupPosts(prev =>
      prev.includes(postId)
        ? prev.filter(id => id !== postId)
        : [...prev, postId]
    );
  };

  const handleSelectOldest = (count: number) => {
    const oldestPosts = sortedPosts.slice(0, count).map(post => post.id);
    setSelectedCleanupPosts(oldestPosts);
  };

  const handleDeleteSelected = () => {
    if (selectedCleanupPosts.length === 0) {
      alert('Please select posts to delete.');
      return;
    }

    if (window.confirm(`Are you sure you want to delete ${selectedCleanupPosts.length} selected post${selectedCleanupPosts.length > 1 ? 's' : ''}?`)) {
      onDeletePosts(selectedCleanupPosts);
      setSelectedCleanupPosts([]);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-4xl w-full max-h-[80vh] overflow-hidden">
        <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">Clean Up Posts</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              You have {posts.length} posts. Select posts to delete to free up space.
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            <XIcon className="w-5 h-5" />
          </button>
        </div>

        <div className="p-6">
          <div className="flex flex-wrap gap-2 mb-4">
            <button
              onClick={() => handleSelectOldest(10)}
              className="px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded text-sm"
            >
              Select 10 Oldest
            </button>
            <button
              onClick={() => handleSelectOldest(25)}
              className="px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded text-sm"
            >
              Select 25 Oldest
            </button>
            <button
              onClick={() => handleSelectOldest(50)}
              className="px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded text-sm"
            >
              Select 50 Oldest
            </button>
            <button
              onClick={() => setSelectedCleanupPosts([])}
              className="px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded text-sm"
            >
              Clear Selection
            </button>
          </div>

          <div className="max-h-96 overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-lg">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 p-4">
              {sortedPosts.map(post => (
                <div
                  key={post.id}
                  className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                    selectedCleanupPosts.includes(post.id)
                      ? 'border-red-300 bg-red-50 dark:border-red-500 dark:bg-red-900/20'
                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                  }`}
                  onClick={() => handleTogglePost(post.id)}
                >
                  <div className="flex items-start space-x-2">
                    <input
                      type="checkbox"
                      checked={selectedCleanupPosts.includes(post.id)}
                      onChange={() => handleTogglePost(post.id)}
                      className="mt-1 rounded border-gray-300 text-red-600"
                    />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-800 dark:text-gray-200 line-clamp-3">
                        {post.content}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                        {new Date(post.timestamp).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="px-6 py-4 bg-gray-50 dark:bg-gray-800/50 border-t border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {selectedCleanupPosts.length} post{selectedCleanupPosts.length !== 1 ? 's' : ''} selected for deletion
          </div>
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              Cancel
            </button>
            <button
              onClick={handleDeleteSelected}
              disabled={selectedCleanupPosts.length === 0}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white rounded-lg disabled:cursor-not-allowed"
            >
              Delete Selected
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export function BulkGenerations() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isWriteModalOpen, setIsWriteModalOpen] = useState(false);
  const [editingPost, setEditingPost] = useState<{ id: string, content: string } | null>(null);
  const [activeFilter, setActiveFilter] = useState<'all' | 'today' | 'week' | 'month' | 'character' | 'general' | 'selected' | 'unselected'>('character');
  const [dateFilterOptions, setDateFilterOptions] = useState<'all' | 'today' | 'week' | 'month'>('all');
  const [showCleanupModal, setShowCleanupModal] = useState(false);

  // Constants
  const MAX_POSTS = 250;
  const WARNING_THRESHOLD = 200;
  const [generatedPosts, setGeneratedPosts] = useState<GeneratedPost[]>([
    {
      id: '1',
      content: "Just launched our new AI-powered content creation tool! 🚀 Create engaging posts in seconds with personalized tone and style. Try it free for 7 days at yourapp.com #AI #ContentCreation",
      timestamp: new Date(),
      isSelected: false,
      characterInfo: {
        id: 'char1',
        name: 'StarBro Official',
        avatar: '/media/GmgegOpaEAIyUoL.jpg'
      }
    },
    {
      id: '2',
      content: "Did you know? 78% of marketers say AI helps them create better content faster. We're seeing amazing results from our beta testers who report 3x engagement! #AIMarketing #SocialMedia",
      timestamp: new Date(Date.now() - 3600000 * 5),
      isSelected: false,
      characterInfo: {
        id: 'char2',
        name: 'StarBro Royal',
        avatar: '/media/GnJdhT1bcAAv-QA.jpeg'
      }
    },
    {
      id: '3',
      content: "The future of content is here - AI that understands your brand voice and audience preferences. Our algorithm gets smarter with every post you create. #ContentStrategy #ArtificialIntelligence",
      timestamp: new Date(Date.now() - 24 * 3600000 * 2), // 2 days ago
      isSelected: false,
      characterInfo: {
        id: 'char3',
        name: 'StarBro Prosperity',
        avatar: '/media/GnJrJvmaoAAK_Od.jpeg'
      }
    },
    {
      id: '4',
      content: "Our AI writer just got a major upgrade! Now with better understanding of tone, style, and audience targeting. Generate weeks of content in minutes! #AIWriter #ContentMarketing",
      timestamp: new Date(Date.now() - 24 * 3600000 * 4), // 4 days ago
      isSelected: false,
      characterInfo: {
        id: 'char4',
        name: 'StarBro Finance',
        avatar: '/media/GnKKhAmbsAEl7E4.jpeg'
      }
    },
    {
      id: '5',
      content: "🔥 Hot take: AI won't replace content creators, but content creators who use AI will replace those who don't. We're making it accessible for everyone. #FutureOfWork #AITechnology",
      timestamp: new Date(Date.now() - 24 * 3600000 * 6), // 6 days ago
      isSelected: false,
      characterInfo: {
        id: 'char1',
        name: 'StarBro Official',
        avatar: '/media/GmgegOpaEAIyUoL.jpg'
      }
    },
    {
      id: '6',
      content: "Join our webinar next Thursday: 'How to 10x Your Social Media Reach with AI Content Generation'. Register now at yourapp.com/webinar #ContentStrategy #SocialMediaTips",
      timestamp: new Date(Date.now() - 24 * 3600000 * 12), // 12 days ago
      isSelected: false,
      characterInfo: {
        id: 'char2',
        name: 'StarBro Royal',
        avatar: '/media/GnJdhT1bcAAv-QA.jpeg'
      }
    },
    {
      id: '7',
      content: "We've analyzed over 1M social media posts to train our AI. The result? Content that consistently outperforms human-only creation by 43%. #DataDriven #AIContent",
      timestamp: new Date(Date.now() - 24 * 3600000 * 30), // 30 days ago
      isSelected: false,
      characterInfo: {
        id: 'char3',
        name: 'StarBro Prosperity',
        avatar: '/media/GnJrJvmaoAAK_Od.jpeg'
      }
    },
    {
      id: '8',
      content: "XRP price continues its upward momentum with a 5.2% gain in the last 24 hours. Technical analysis suggests strong support at the $1.75 level. #XRP #Crypto #TradingAnalysis",
      timestamp: new Date(Date.now() - 3600000 * 2), // 2 hours ago
      isSelected: false
    },
    {
      id: '9',
      content: "Breaking: Major financial institution announces plans to integrate XRP for cross-border payments, citing 90% reduction in transaction costs and settlement times measured in seconds rather than days. #XRPLedger #FinTech #Blockchain",
      timestamp: new Date(Date.now() - 3600000 * 8), // 8 hours ago
      isSelected: false
    },
    {
      id: '10',
      content: "📊 Weekly market report: XRP showing strong resilience despite broader market volatility. The XRP/USD pair maintained key support levels while volume indicators suggest accumulation by institutional investors. #CryptoMarkets #XRPCommunity",
      timestamp: new Date(Date.now() - 3600000 * 20), // 20 hours ago
      isSelected: false
    },
    {
      id: '11',
      content: "Reminder: The XRP Global Summit starts next week with keynote speakers from Ripple, major banks, and regulatory experts. Virtual attendance options available at xrpsummit.com #XRPSummit2025 #Blockchain #Innovation",
      timestamp: new Date(Date.now() - 24 * 3600000), // 1 day ago
      isSelected: false
    },
    {
      id: '12',
      content: "New tutorial: How to set up your first XRP wallet and secure your assets with best practices for key management and backup solutions. Watch now on our channel! #XRPTutorial #CryptoSecurity #Wallets",
      timestamp: new Date(Date.now() - 24 * 3600000 * 3), // 3 days ago
      isSelected: false
    },
    {
      id: '13',
      content: "The future of decentralized finance is interoperable blockchains. XRP Ledger's built-in DEX and cross-currency features position it as a key player in this evolving ecosystem. #DeFi #Interoperability #XRPLedger",
      timestamp: new Date(Date.now() - 24 * 3600000 * 5), // 5 days ago
      isSelected: false
    }
  ]);
  const [isSaving, setIsSaving] = useState(false);
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});
  const [dateFilters, setDateFilters] = useState<Record<string, string>>({});
  
  // Helper function to format dates
  const formatDate = (date: Date) => {
    return date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric'
    });
  };

  // Handle adding new generated posts to the list
  const handleGeneratedPosts = (newPosts: GeneratedPost[]) => {
    // Check if adding new posts would exceed the limit
    if (generatedPosts.length + newPosts.length > MAX_POSTS) {
      alert(`Cannot generate posts. You would exceed the maximum limit of ${MAX_POSTS} posts. Please delete some existing posts first.`);
      return;
    }

    // Add current timestamp to all posts
    const postsWithTimestamp = newPosts.map(post => ({
      ...post,
      timestamp: new Date()
    }));
    setGeneratedPosts([...postsWithTimestamp, ...generatedPosts]);
    setIsModalOpen(false);

    // Show warning if approaching limit
    const newTotal = generatedPosts.length + newPosts.length;
    if (newTotal >= WARNING_THRESHOLD && newTotal < MAX_POSTS) {
      alert(`Warning: You have ${newTotal} posts. You're approaching the limit of ${MAX_POSTS} posts. Consider cleaning up old posts.`);
    }
  };
  
  // Handle toggling selection of a post
  const togglePostSelection = (postId: string) => {
    setGeneratedPosts(prev => 
      prev.map(post => 
        post.id === postId 
          ? { ...post, isSelected: !post.isSelected } 
          : post
      )
    );
  };
  
  // Handle copying post content to clipboard
  const copyPostToClipboard = (content: string) => {
    navigator.clipboard.writeText(content);
    // Here you could add a toast notification for feedback
    alert('Copied to clipboard!');
  };
  
  // Handle saving selected posts to schedule
  const handleSaveToSchedule = () => {
    setIsSaving(true);
    
    // Simulate API call delay
    setTimeout(() => {
      // Here you would make an actual API call to save the posts
      console.log('Saving posts to schedule:', generatedPosts.filter(post => post.isSelected));
      
      // Reset selections and show success
      setGeneratedPosts(prev => 
        prev.map(post => ({ ...post, isSelected: false }))
      );
      
      setIsSaving(false);
      
      // Show success notification
      alert('Posts saved to schedule!');
    }, 1500);
  };

  // Handle deleting a post
  const handleDeletePost = (postId: string) => {
    if (window.confirm("Are you sure you want to delete this post?")) {
      setGeneratedPosts(prev => prev.filter(post => post.id !== postId));
    }
  };

  // Handle editing a post
  const handleEditPost = (postId: string, postContent: string) => {
    setEditingPost({ id: postId, content: postContent });
  };

  // Save edited post
  const handleSaveEdit = () => {
    if (!editingPost) return;
    
    setGeneratedPosts(prev => 
      prev.map(post => 
        post.id === editingPost.id 
          ? { ...post, content: editingPost.content } 
          : post
      )
    );
    
    setEditingPost(null);
  };

  // Cancel editing
  const handleCancelEdit = () => {
    setEditingPost(null);
  };
  
  // Get filtered posts based on active filter
  const filteredPosts = useMemo(() => {
    // Apply date filter if not in character or general view
    const dateFilteredPosts = generatedPosts.filter(post => {
      if (activeFilter === 'character' || activeFilter === 'general') {
        return true; // Date filtering happens at character level for these views
      }
      
      // Apply date filtering for non-character/general views
      if (dateFilterOptions !== 'all') {
        const postDate = new Date(post.timestamp);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        if (dateFilterOptions === 'today') {
          const postDay = new Date(postDate);
          postDay.setHours(0, 0, 0, 0);
          if (postDay.getTime() !== today.getTime()) return false;
        } else if (dateFilterOptions === 'week') {
          const weekAgo = new Date(today);
          weekAgo.setDate(today.getDate() - 7);
          if (postDate < weekAgo) return false;
        } else if (dateFilterOptions === 'month') {
          const monthAgo = new Date(today);
          monthAgo.setMonth(today.getMonth() - 1);
          if (postDate < monthAgo) return false;
        }
      }
      
      return true;
    });
    
    // Apply main filter categories
    if (activeFilter === 'all') {
      return dateFilteredPosts;
    } else if (activeFilter === 'selected') {
      return dateFilteredPosts.filter(post => post.isSelected);
    } else if (activeFilter === 'unselected') {
      return dateFilteredPosts.filter(post => !post.isSelected);
    } else if (activeFilter === 'character') {
      return dateFilteredPosts.filter(post => post.characterInfo);
    } else if (activeFilter === 'general') {
      return dateFilteredPosts.filter(post => !post.characterInfo);
    }
    
    return dateFilteredPosts;
  }, [activeFilter, dateFilterOptions, generatedPosts]);

  // Count selected posts
  const selectedCount = generatedPosts.filter(post => post.isSelected).length;

  // Toggle section expansion
  const toggleSection = (charId: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [charId]: !prev[charId]
    }));
  };

  // Filter posts by date for a specific character
  const getFilteredPostsForCharacter = (posts: GeneratedPost[], characterId: string) => {
    // Apply date filter if exists for this character
    const dateFilter = dateFilters[characterId] || 'all';
    
    return posts.filter(post => {
      // Apply date filtering
      if (dateFilter !== 'all') {
        const postDate = new Date(post.timestamp);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        if (dateFilter === 'today') {
          const postDay = new Date(postDate);
          postDay.setHours(0, 0, 0, 0);
          if (postDay.getTime() !== today.getTime()) return false;
        } else if (dateFilter === 'week') {
          const weekAgo = new Date(today);
          weekAgo.setDate(today.getDate() - 7);
          if (postDate < weekAgo) return false;
        } else if (dateFilter === 'month') {
          const monthAgo = new Date(today);
          monthAgo.setMonth(today.getMonth() - 1);
          if (postDate < monthAgo) return false;
        }
      }
      
      return true;
    }).filter(post => {
      // Apply active filter
      if (activeFilter === 'scheduled') {
        return post.isScheduled;
      } else if (activeFilter === 'drafts') {
        return !post.isScheduled && post.isDraft;
      } else {
        return !post.isScheduled && !post.isDraft;
      }
    });
  };

  // Handle date filter change for a character
  const handleDateFilterChange = (charId: string, filter: string) => {
    setDateFilters(prev => ({
      ...prev,
      [charId]: filter
    }));
  };

  // Type for character groupings in post list
  interface CharacterGroup {
    id: string;
    name: string;
    avatar: string;
    posts: GeneratedPost[];
  }

  // Group posts by character
  const postsByCharacter = useMemo(() => {
    const grouped: Record<string, GeneratedPost[]> = {};
    const uncategorized: GeneratedPost[] = [];
    
    generatedPosts.forEach(post => {
      if (post.characterInfo) {
        const charId = post.characterInfo.id;
        if (!grouped[charId]) {
          grouped[charId] = [];
        }
        grouped[charId].push(post);
      } else {
        uncategorized.push(post);
      }
    });
    
    // Convert to array of character groups
    const result: CharacterGroup[] = Object.keys(grouped).map(charId => {
      const character = sampleCharacters.find(c => c.id === charId);
      return {
        id: charId,
        name: character?.name || 'Unknown Character',
        avatar: character?.avatar || '/images/default-avatar.png',
        posts: grouped[charId],
      };
    });
    
    // Add uncategorized as a special group if there are any
    if (uncategorized.length > 0) {
      result.push({
        id: 'general',
        name: 'General Posts',
        avatar: '/images/default-avatar.png',
        posts: uncategorized,
      });
    }
    
    return result;
  }, [generatedPosts]);

  function showGeneralPostsSection() {
    // Only show the General Posts section if there are uncategorized posts
    return generatedPosts.some(post => !post.characterInfo);
  }

  const handleSubmitPost = (newPost: GeneratedPost) => {
    // Check if adding new post would exceed the limit
    if (generatedPosts.length >= MAX_POSTS) {
      alert(`Cannot add post. You have reached the maximum limit of ${MAX_POSTS} posts. Please delete some existing posts first.`);
      return;
    }

    setGeneratedPosts(prev => [...prev, newPost]);

    // Show warning if approaching limit
    if (generatedPosts.length + 1 >= WARNING_THRESHOLD && generatedPosts.length + 1 < MAX_POSTS) {
      alert(`Warning: You have ${generatedPosts.length + 1} posts. You're approaching the limit of ${MAX_POSTS} posts. Consider cleaning up old posts.`);
    }
  };

  // Bulk actions
  const handleSelectAll = () => {
    setGeneratedPosts(prev => prev.map(post => ({ ...post, isSelected: true })));
  };

  const handleDeselectAll = () => {
    setGeneratedPosts(prev => prev.map(post => ({ ...post, isSelected: false })));
  };

  const handleDeleteSelected = () => {
    const selectedCount = generatedPosts.filter(post => post.isSelected).length;
    if (selectedCount === 0) {
      alert('No posts selected for deletion.');
      return;
    }

    if (window.confirm(`Are you sure you want to delete ${selectedCount} selected post${selectedCount > 1 ? 's' : ''}? This action cannot be undone.`)) {
      setGeneratedPosts(prev => prev.filter(post => !post.isSelected));
    }
  };

  const handleCleanupOldPosts = () => {
    setShowCleanupModal(true);
  };

  const handleCleanupDeletePosts = (postIds: string[]) => {
    setGeneratedPosts(prev => prev.filter(post => !postIds.includes(post.id)));
  };

  return (
    <PageLayout 
      icon={FileText}
      title="Bulk Generations"
      actions={
        <div className="flex space-x-3">
          <button
            onClick={() => setIsWriteModalOpen(true)}
            className="px-4 py-2 bg-white border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-lg shadow-sm flex items-center space-x-2"
          >
            <FileText className="w-4 h-4" />
            <span>Write Post</span>
          </button>
          <button
            onClick={() => setIsModalOpen(true)}
            className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg shadow-sm flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>Generate Posts</span>
          </button>
        </div>
      }
    >
      {editingPost && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Edit Post</h3>
            <textarea
              value={editingPost.content}
              onChange={(e) => setEditingPost({ ...editingPost, content: e.target.value })}
              className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 text-gray-900 dark:text-white resize-none mb-4"
              rows={6}
            />
            <div className="flex justify-end space-x-3">
              <button
                onClick={handleCancelEdit}
                className="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white rounded-lg"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveEdit}
                className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-500 dark:hover:bg-indigo-600 text-white rounded-lg"
              >
                Save Changes
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Post count and limit warning */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden mb-4">
        <div className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-600 dark:text-gray-400">
                <span className="font-medium">{generatedPosts.length}</span> of {MAX_POSTS} posts
              </div>
              {generatedPosts.length >= WARNING_THRESHOLD && (
                <div className="flex items-center space-x-2 text-amber-600 dark:text-amber-400">
                  <AlertTriangle className="w-4 h-4" />
                  <span className="text-sm font-medium">
                    {generatedPosts.length >= MAX_POSTS
                      ? 'Post limit reached'
                      : 'Approaching post limit'
                    }
                  </span>
                </div>
              )}
            </div>
            {generatedPosts.length >= WARNING_THRESHOLD && (
              <button
                onClick={handleCleanupOldPosts}
                className="px-3 py-1 bg-amber-100 hover:bg-amber-200 dark:bg-amber-900/30 dark:hover:bg-amber-900/50 text-amber-700 dark:text-amber-300 rounded-lg text-sm font-medium"
              >
                Clean Up Posts
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Bulk actions bar */}
      {selectedCount > 0 && (
        <div className="bg-indigo-50 dark:bg-indigo-900/30 border border-indigo-200 dark:border-indigo-700 rounded-xl p-4 mb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
                <span className="text-sm font-medium text-indigo-800 dark:text-indigo-200">
                  {selectedCount} post{selectedCount > 1 ? 's' : ''} selected
                </span>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={handleDeselectAll}
                className="px-3 py-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg text-sm hover:bg-gray-50 dark:hover:bg-gray-600"
              >
                Deselect All
              </button>
              <button
                onClick={handleDeleteSelected}
                className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm flex items-center space-x-1"
              >
                <Trash className="w-4 h-4" />
                <span>Delete Selected</span>
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden mb-6">
        <div className="p-6">
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-medium text-gray-900 dark:text-white">Generated Posts</h3>
              <div className="flex items-center space-x-2">
                <button
                  onClick={handleSelectAll}
                  className="px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg text-sm flex items-center space-x-1"
                >
                  <CheckSquare className="w-4 h-4" />
                  <span>Select All</span>
                </button>
                <button
                  onClick={handleDeselectAll}
                  className="px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg text-sm flex items-center space-x-1"
                >
                  <Square className="w-4 h-4" />
                  <span>Deselect All</span>
                </button>
              </div>
            </div>
            <div className="flex items-center space-x-2 mb-4 overflow-x-auto pb-2 border-b border-gray-200 dark:border-gray-700">
              <button
                onClick={() => setActiveFilter('character')}
                className={`px-3 py-1.5 rounded-md text-sm font-medium flex items-center ${
                  activeFilter === 'character' 
                    ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300' 
                    : 'bg-white text-gray-700 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                }`}
              >
                <User className="w-4 h-4 inline-block mr-1.5" />
                <span>By Character</span>
              </button>
              <button
                onClick={() => setActiveFilter('general')}
                className={`px-3 py-1.5 rounded-md text-sm font-medium flex items-center ${
                  activeFilter === 'general' 
                    ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300' 
                    : 'bg-white text-gray-700 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                }`}
              >
                <FileText className="w-4 h-4 inline-block mr-1.5" />
                <span>General Posts</span>
              </button>
              <button
                onClick={() => setActiveFilter('all')}
                className={`px-3 py-1.5 rounded-md text-sm font-medium flex items-center ${
                  activeFilter === 'all' 
                    ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300' 
                    : 'bg-white text-gray-700 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                }`}
              >
                <ListOrdered className="w-4 h-4 inline-block mr-1.5" />
                <span>All Posts</span>
              </button>
              <button
                onClick={() => setActiveFilter('selected')}
                className={`px-3 py-1.5 rounded-md text-sm font-medium flex items-center ${
                  activeFilter === 'selected' 
                    ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300' 
                    : 'bg-white text-gray-700 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                }`}
              >
                <CheckCircle className="w-4 h-4 inline-block mr-1.5" />
                <span>Selected</span>
              </button>
              <button
                onClick={() => setActiveFilter('unselected')}
                className={`px-3 py-1.5 rounded-md text-sm font-medium flex items-center ${
                  activeFilter === 'unselected' 
                    ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300' 
                    : 'bg-white text-gray-700 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                }`}
              >
                <Minus className="w-4 h-4 inline-block mr-1.5" />
                <span>Unselected</span>
              </button>
            </div>
            
            {(activeFilter === 'all' || activeFilter === 'selected' || activeFilter === 'unselected') && (
              <div className="flex items-center space-x-2 mb-6 overflow-x-auto pb-2">
                <span className="text-sm text-gray-500 dark:text-gray-400 mr-2">Filter by date:</span>
                <button
                  onClick={() => setDateFilterOptions('all')}
                  className={`px-3 py-1 rounded-md text-xs font-medium flex items-center ${
                    dateFilterOptions === 'all' 
                      ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300' 
                      : 'bg-white text-gray-500 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600'
                  }`}
                >
                  <Calendar className="w-3.5 h-3.5 inline-block mr-1.5" />
                  <span>All Time</span>
                </button>
                <button
                  onClick={() => setDateFilterOptions('today')}
                  className={`px-3 py-1 rounded-md text-xs font-medium flex items-center ${
                    dateFilterOptions === 'today' 
                      ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300' 
                      : 'bg-white text-gray-500 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600'
                  }`}
                >
                  <Calendar className="w-3.5 h-3.5 inline-block mr-1.5" />
                  <span>Today</span>
                </button>
                <button
                  onClick={() => setDateFilterOptions('week')}
                  className={`px-3 py-1 rounded-md text-xs font-medium flex items-center ${
                    dateFilterOptions === 'week' 
                      ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300' 
                      : 'bg-white text-gray-500 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600'
                  }`}
                >
                  <Calendar className="w-3.5 h-3.5 inline-block mr-1.5" />
                  <span>This Week</span>
                </button>
                <button
                  onClick={() => setDateFilterOptions('month')}
                  className={`px-3 py-1 rounded-md text-xs font-medium flex items-center ${
                    dateFilterOptions === 'month' 
                      ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300' 
                      : 'bg-white text-gray-500 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600'
                  }`}
                >
                  <Calendar className="w-3.5 h-3.5 inline-block mr-1.5" />
                  <span>This Month</span>
                </button>
              </div>
            )}
            
            {filteredPosts.length === 0 && (
              <div className="p-8 text-center text-gray-500 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <div className="flex justify-center mb-4">
                  <SearchX className="w-12 h-12 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">No posts found</h3>
                <p className="text-gray-500 dark:text-gray-400">
                  {activeFilter === 'character' ? (
                    <>No posts associated with characters. Try generating some posts with a character.</>
                  ) : activeFilter === 'general' ? (
                    <>No general posts found. Try generating some posts without selecting a character.</>
                  ) : activeFilter === 'selected' ? (
                    <>No selected posts found. Try selecting some posts from the list.</>
                  ) : activeFilter === 'unselected' ? (
                    <>All posts are currently selected. You can unselect posts by clicking on them.</>
                  ) : (
                    <>No posts matching the current filter. Try generating some posts or changing the filter.</>
                  )}
                </p>
              </div>
            )}
            
            {activeFilter !== 'character' ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
                {filteredPosts.length > 0 ? (
                  filteredPosts.map(post => (
                    <div 
                      key={post.id} 
                      className={`bg-white dark:bg-gray-800 border ${
                        post.isSelected 
                          ? 'border-indigo-300 dark:border-indigo-500' 
                          : 'border-gray-200 dark:border-gray-700'
                      } rounded-lg shadow-sm overflow-hidden flex flex-col`}
                    >
                      <div className="p-3 h-40 overflow-y-auto text-sm text-gray-800 dark:text-gray-200 flex-grow">
                        {post.content}
                      </div>
                      <div className="bg-gray-50 dark:bg-gray-800/50 p-2 border-t border-gray-200 dark:border-gray-700 flex justify-between items-center">
                        <div>
                          <label className="inline-flex items-center">
                            <input 
                              type="checkbox" 
                              className="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                              checked={post.isSelected}
                              onChange={() => togglePostSelection(post.id)}
                            />
                            <span className="ml-2 text-xs text-gray-600 dark:text-gray-400">
                              {post.timestamp ? formatDate(post.timestamp) : 'Unknown date'}
                            </span>
                          </label>
                        </div>
                        <div className="flex space-x-1">
                          <button
                            onClick={() => handleEditPost(post.id, post.content)}
                            className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                          >
                            <Edit className="w-3.5 h-3.5" />
                          </button>
                          <button 
                            onClick={() => handleDeletePost(post.id)}
                            className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                          >
                            <Trash2 className="w-3.5 h-3.5" />
                          </button>
                          <button
                            onClick={() => {
                              navigator.clipboard.writeText(post.content);
                              alert('Post copied to clipboard');
                            }}
                            className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                          >
                            <Copy className="w-3.5 h-3.5" />
                          </button>
                        </div>
                      </div>
                      <div className="p-3 bg-gray-50 dark:bg-gray-800/70 border-t border-gray-200 dark:border-gray-700 flex justify-between items-center">
                        {post.characterInfo && (
                          <div className="flex items-center">
                            <div className="h-10 w-10 rounded-lg overflow-hidden mr-3 bg-gray-100 dark:bg-gray-700">
                              <img src={post.characterInfo.avatar} alt={post.characterInfo.name} className="h-full w-full object-cover" />
                            </div>
                            <div>
                              <p className="text-sm font-medium text-gray-900 dark:text-white">{post.characterInfo.name}</p>
                              <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                                <Bot className="w-3 h-3 mr-1" />
                                <span>Star Bro</span>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="col-span-full text-center py-12 text-gray-500 dark:text-gray-400">
                    <FileImage className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-600 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-1">No posts found</h3>
                    <p>Try adjusting your filters or generate new posts.</p>
                  </div>
                )}
              </div>
            ) : (
              // Character grouped view
              <div className="space-y-8">
                {Object.keys(postsByCharacter).length > 0 ? (
                  <>
                    {/* Display posts grouped by character */}
                    {postsByCharacter.map((group) => {
                      // Get character info for the section header
                      const isExpanded = expandedSections[group.id] !== false; // Default to expanded
                      const filteredPosts = getFilteredPostsForCharacter(group.posts, group.id);
                      const currentDateFilter = dateFilters[group.id] || 'all';
                      
                      return (
                        <div key={group.id} className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                          {/* Character header with toggle */}
                          <div 
                            className="bg-gray-50 dark:bg-gray-800 p-4 border-b border-gray-200 dark:border-gray-700 cursor-pointer"
                            onClick={() => toggleSection(group.id)}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center">
                                <div className="h-12 w-12 rounded-lg overflow-hidden mr-4 bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600">
                                  <img src={group.avatar} alt={group.name} className="h-full w-full object-cover" />
                                </div>
                                <div>
                                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">{group.name}</h3>
                                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                    <Bot className="w-4 h-4 mr-1" />
                                    <span>Star Bro • {group.posts.length} post{group.posts.length !== 1 ? 's' : ''}</span>
                                  </div>
                                </div>
                              </div>
                              {isExpanded ? (
                                <ChevronUp className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                              ) : (
                                <ChevronDown className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                              )}
                            </div>
                          </div>
                          
                          {isExpanded && (
                            <>
                              {/* Date filter section */}
                              <div className="bg-gray-50/50 dark:bg-gray-800/50 px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                <div className="flex flex-wrap gap-2">
                                  <button
                                    onClick={() => handleDateFilterChange(group.id, 'all')}
                                    className={`px-3 py-1 rounded-md text-xs font-medium flex items-center ${
                                      currentDateFilter === 'all' 
                                        ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300' 
                                        : 'bg-white text-gray-700 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                                    } border border-gray-200 dark:border-gray-600`}
                                  >
                                    <Calendar className="w-3.5 h-3.5 inline-block mr-1.5" />
                                    <span>All Posts</span>
                                  </button>
                                  <button
                                    onClick={() => handleDateFilterChange(group.id, 'today')}
                                    className={`px-3 py-1 rounded-md text-xs font-medium flex items-center ${
                                      currentDateFilter === 'today' 
                                        ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300' 
                                        : 'bg-white text-gray-700 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                                    } border border-gray-200 dark:border-gray-600`}
                                  >
                                    <Calendar className="w-3.5 h-3.5 inline-block mr-1.5" />
                                    <span>Today</span>
                                  </button>
                                  <button
                                    onClick={() => handleDateFilterChange(group.id, 'week')}
                                    className={`px-3 py-1 rounded-md text-xs font-medium flex items-center ${
                                      currentDateFilter === 'week' 
                                        ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300' 
                                        : 'bg-white text-gray-700 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                                    } border border-gray-200 dark:border-gray-600`}
                                  >
                                    <Calendar className="w-3.5 h-3.5 inline-block mr-1.5" />
                                    <span>This Week</span>
                                  </button>
                                  <button
                                    onClick={() => handleDateFilterChange(group.id, 'month')}
                                    className={`px-3 py-1 rounded-md text-xs font-medium flex items-center ${
                                      currentDateFilter === 'month' 
                                        ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300' 
                                        : 'bg-white text-gray-700 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                                    } border border-gray-200 dark:border-gray-600`}
                                  >
                                    <Calendar className="w-3.5 h-3.5 inline-block mr-1.5" />
                                    <span>This Month</span>
                                  </button>
                                  <div className="ml-auto flex items-center text-xs text-gray-500 dark:text-gray-400">
                                    <Calendar className="w-3 h-3 mr-1" />
                                    <span>
                                      {filteredPosts.length} post{filteredPosts.length !== 1 ? 's' : ''} 
                                      {currentDateFilter !== 'all' ? ` (${currentDateFilter})` : ''}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            
                              {/* Posts grid */}
                              <div className="p-4">
                                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
                                  {filteredPosts.length > 0 ? (
                                    filteredPosts.map(post => (
                                      <div 
                                        key={post.id} 
                                        className={`bg-white dark:bg-gray-800 border ${
                                          post.isSelected 
                                            ? 'border-indigo-300 dark:border-indigo-500' 
                                            : 'border-gray-200 dark:border-gray-700'
                                        } rounded-lg shadow-sm overflow-hidden flex flex-col`}
                                      >
                                        <div className="p-3 h-40 overflow-y-auto text-sm text-gray-800 dark:text-gray-200 flex-grow">
                                          {post.content}
                                        </div>
                                        <div className="bg-gray-50 dark:bg-gray-800/50 p-2 border-t border-gray-200 dark:border-gray-700 flex justify-between items-center">
                                          <div>
                                            <label className="inline-flex items-center">
                                              <input 
                                                type="checkbox" 
                                                className="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                                checked={post.isSelected}
                                                onChange={() => togglePostSelection(post.id)}
                                              />
                                              <span className="ml-2 text-xs text-gray-600 dark:text-gray-400">
                                                {post.timestamp ? formatDate(post.timestamp) : 'Unknown date'}
                                              </span>
                                            </label>
                                          </div>
                                          <div className="flex space-x-1">
                                            <button
                                              onClick={() => handleEditPost(post.id, post.content)}
                                              className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                                            >
                                              <Edit className="w-3.5 h-3.5" />
                                            </button>
                                            <button 
                                              onClick={() => handleDeletePost(post.id)}
                                              className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                                            >
                                              <Trash2 className="w-3.5 h-3.5" />
                                            </button>
                                            <button
                                              onClick={() => {
                                                navigator.clipboard.writeText(post.content);
                                                alert('Post copied to clipboard');
                                              }}
                                              className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                                            >
                                              <Copy className="w-3.5 h-3.5" />
                                            </button>
                                          </div>
                                        </div>
                                      </div>
                                    ))
                                  ) : (
                                    <div className="text-center py-12 text-gray-500 dark:text-gray-400">
                                      <FileImage className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-600 mb-4" />
                                      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-1">No character posts found</h3>
                                      <p>Try generating posts from the Character Studio.</p>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </>
                          )}
                        </div>
                      );
                    })}
                  </>
                ) : (
                  <div className="text-center py-12 text-gray-500 dark:text-gray-400">
                    <FileImage className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-600 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-1">No character posts found</h3>
                    <p>Try generating posts from the Character Studio.</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Render the generation modal */}
      <GenerationModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onGenerate={handleGeneratedPosts}
        currentPostCount={generatedPosts.length}
      />
      <WritePostModal
        isOpen={isWriteModalOpen}
        onClose={() => setIsWriteModalOpen(false)}
        onSubmit={handleSubmitPost}
      />
      <CleanupModal
        isOpen={showCleanupModal}
        onClose={() => setShowCleanupModal(false)}
        posts={generatedPosts}
        onDeletePosts={handleCleanupDeletePosts}
      />
    </PageLayout>
  );
}

export default BulkGenerations;
