import { useState, useEffect, useRef, useCallback, useMemo } from "react";
import {
  Calendar as CalendarIcon,
  Plus,
  Globe2,
  AlertCircle,
} from "lucide-react";
import { DayView } from "./DayView";
import { WeekView } from "./WeekView";
import { MonthView } from "./MonthView";
import { TimeIntervalSelector } from "./TimeIntervalSelector";

import { PageLayout } from "../common/PageLayout";
import { EventModal } from "./EventModal";
import { useScheduleStore } from "../../stores/scheduleStore";
import toast from "react-hot-toast";

export function Schedule() {
  const [view, setView] = useState(() =>
    window.innerWidth < 768 ? "day" : "week",
  );
  const [currentDate, setCurrentDate] = useState(new Date());
  const [isBulkCreateOpen, setIsBulkCreateOpen] = useState(false);
  const [timeZone, setTimeZone] = useState(
    Intl.DateTimeFormat().resolvedOptions().timeZone,
  );
  const [showTimeZoneSelector, setShowTimeZoneSelector] = useState(false);
  const [error, setError] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedEventId, setSelectedEventId] = useState(null);
  const [selectedTime, setSelectedTime] = useState(null);

  const {
    events,
    addEvent,
    updateEvent,
    deleteEvent,
    timeInterval,
    setTimeInterval,
  } = useScheduleStore();
  const timeZoneSelectorRef = useRef(null);

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768 && view !== "day") setView("day");
    };
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [view]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        timeZoneSelectorRef.current &&
        !timeZoneSelectorRef.current.contains(event.target)
      ) {
        setShowTimeZoneSelector(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  useEffect(() => {
    const newDate = new Date(currentDate.toLocaleString("en-US", { timeZone }));
    setCurrentDate(newDate);
  }, [timeZone]);

  const handleAddEvent = (date, time) => {
    const id = crypto.randomUUID();
    addEvent({ id, title: "New Event", date, time });
    setSelectedEventId(id);
    setIsModalOpen(true);
    setError(null);
    toast.success("Event created");
  };

  const handleEventClick = (eventId) => {
    setSelectedEventId(eventId);
    setIsModalOpen(true);
  };

  const handleModalSave = (data) => {
    if (!selectedEventId) return;
    try {
      const typeLabels = data.types
        .map((t) => t.charAt(0).toUpperCase() + t.slice(1))
        .join(" + ");
      updateEvent(selectedEventId, {
        title: `${typeLabels} - ${data.platform}`,
        contentTypes: data.types,
        platform: data.platform,
        content: data.content,
      });
      setIsModalOpen(false);
      setSelectedEventId(null);
      toast.success("Event updated");
    } catch (err) {
      setError("Failed to save event");
      toast.error("Error saving event");
    }
  };

  const handleDeleteEvent = (eventId) => {
    try {
      deleteEvent(eventId);
      setIsModalOpen(false);
      setSelectedEventId(null);
      toast.success("Event deleted");
    } catch (err) {
      setError("Failed to delete event");
      toast.error("Error deleting event");
    }
  };

  const selectedEvent = useMemo(
    () => events.find((e) => e.id === selectedEventId),
    [selectedEventId, events],
  );

  const handleViewChange = (newView) => {
    if (
      window.innerWidth < 768 &&
      (newView === "week" || newView === "month")
    ) {
      setView("day");
      toast.success("Week and month views are available on larger screens");
    } else {
      setView(newView);
      toast.success(`Switched to ${newView} view`);
    }
  };

  return (
    <PageLayout
      icon={CalendarIcon}
      title="Schedule"
      actions={
        <button
          onClick={() => setIsBulkCreateOpen(true)}
          className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 text-sm"
        >
          <Plus className="w-4 h-4" />
          <span>Bulk Create</span>
        </button>
      }
    >
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between">
          <TimeIntervalSelector
            interval={timeInterval}
            onChange={setTimeInterval}
          />
          <div className="flex space-x-2">
            <button
              onClick={() => setShowTimeZoneSelector(!showTimeZoneSelector)}
              className="text-sm"
            >
              <Globe2 className="w-4 h-4 inline" /> {timeZone}
            </button>
            <div className="flex space-x-1 bg-gray-100 rounded p-1">
              {["day", "week", "month"].map((v) => (
                <button
                  key={v}
                  className={`px-2 py-1 text-xs rounded ${view === v ? "bg-white shadow text-black" : "text-gray-500"}`}
                  onClick={() => handleViewChange(v)}
                >
                  {v.charAt(0).toUpperCase() + v.slice(1)}
                </button>
              ))}
            </div>
          </div>
        </div>

        {error && (
          <div className="flex items-center space-x-2 text-red-600">
            <AlertCircle className="w-4 h-4" />
            <span>{error}</span>
          </div>
        )}

        <div className="bg-white rounded-xl shadow-sm border overflow-hidden">
          {view === "day" && (
            <DayView
              currentDate={currentDate}
              events={events}
              timeInterval={timeInterval}
              onAddEvent={handleAddEvent}
              onEventClick={handleEventClick}
            />
          )}
          {view === "week" && (
            <WeekView
              currentDate={currentDate}
              events={events}
              timeInterval={timeInterval}
              onAddEvent={handleAddEvent}
              onEventClick={handleEventClick}
              onDayClick={setCurrentDate}
            />
          )}
          {view === "month" && (
            <MonthView
              currentDate={currentDate}
              events={events}
              onDayClick={setCurrentDate}
              onWeekClick={setCurrentDate}
              onAddEvent={handleAddEvent}
              onEventClick={handleEventClick}
            />
          )}
        </div>
      </div>

      {isBulkCreateOpen && (
        <BulkCreateModal
          isOpen={isBulkCreateOpen}
          onClose={() => setIsBulkCreateOpen(false)}
          currentDate={currentDate}
        />
      )}

      <EventModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setSelectedEventId(null);
          setSelectedTime(null);
          setError(null);
        }}
        onSave={handleModalSave}
        onDelete={
          selectedEventId ? () => handleDeleteEvent(selectedEventId) : undefined
        }
        isExisting={!!selectedEventId}
        event={selectedEvent}
      />
    </PageLayout>
  );
}
