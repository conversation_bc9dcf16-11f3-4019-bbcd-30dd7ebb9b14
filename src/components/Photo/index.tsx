import React, { useState, useRef, useCallback } from "react";
import {
  Image as ImageIcon,
  Plus,
  Settings2,
  Trash2,
  CheckCircle2,
  Upload,
  <PERSON>rkles,
  ChevronDown,
  ChevronUp,
  X,
  Camera,
  FileImage,
  CheckSquare,
  Square,
  Trash,
  Al<PERSON>Triangle,
} from "lucide-react";
import { TokenBalance } from "../Dashboard/TokenBalance";
import { useTokens } from "../../hooks/useTokens";
import { PhotoEditor } from "./PhotoEditor";
import { PageLayout } from "../common/PageLayout";
import { useNavigate } from "react-router-dom";

export interface Photo {
  id: string;
  title: string;
  resolution: string;
  format: string;
  style: string[];
  aspectRatio: string;
  thumbnailUrl: string;
  url: string;
  isGenerated?: boolean;
  isSelected?: boolean;
  settings?: {
    quality: string;
    fps: number;
    bitrate: string;
    codec: string;
  };
}

const samplePhotos: Photo[] = [
  {
    id: "1",
    title: "Modern Tech Workspace",
    resolution: "3840x2160",
    format: "JPEG",
    style: ["Modern", "Corporate", "Minimalist"],
    aspectRatio: "16:9",
    thumbnailUrl:
      "https://images.unsplash.com/photo-1497215728101-856f4ea42174?w=800&auto=format&fit=crop",
    url: "https://images.unsplash.com/photo-1497215728101-856f4ea42174?w=1920&auto=format&fit=crop",
    isGenerated: false,
    isSelected: false,
    settings: {
      quality: "Ultra",
      fps: 60,
      bitrate: "12 Mbps",
      codec: "H.264",
    },
  },
  {
    id: "2",
    title: "Urban Coffee Experience",
    resolution: "2560x1440",
    format: "JPEG",
    style: ["Urban", "Lifestyle", "Warm"],
    aspectRatio: "16:9",
    thumbnailUrl:
      "https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=800&auto=format&fit=crop",
    url: "https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=1920&auto=format&fit=crop",
    isGenerated: true,
    isSelected: false,
    settings: {
      quality: "High",
      fps: 60,
      bitrate: "8 Mbps",
      codec: "H.264",
    },
  },
  {
    id: "4",
    title: "Natural Landscape",
    resolution: "3840x2160",
    format: "JPEG",
    style: ["Natural", "Cinematic", "Peaceful"],
    aspectRatio: "16:9",
    thumbnailUrl:
      "https://images.unsplash.com/photo-1472214103451-9374bd1c798e?w=800&auto=format&fit=crop",
    url: "https://images.unsplash.com/photo-1472214103451-9374bd1c798e?w=1920&auto=format&fit=crop",
    isGenerated: false,
    isSelected: false,
    settings: {
      quality: "Ultra",
      fps: 60,
      bitrate: "12 Mbps",
      codec: "H.264",
    },
  },
  {
    id: "5",
    title: "Urban Night Life",
    resolution: "2560x1440",
    format: "JPEG",
    style: ["Urban", "Night", "Dynamic"],
    aspectRatio: "16:9",
    thumbnailUrl:
      "https://images.unsplash.com/photo-1519501025264-65ba15a82390?w=800&auto=format&fit=crop",
    url: "https://images.unsplash.com/photo-1519501025264-65ba15a82390?w=1920&auto=format&fit=crop",
    isGenerated: true,
    isSelected: false,
    settings: {
      quality: "High",
      fps: 60,
      bitrate: "8 Mbps",
      codec: "H.264",
    },
  },
  {
    id: "6",
    title: "Minimalist Portrait",
    resolution: "2560x1440",
    format: "JPEG",
    style: ["Minimalist", "Portrait", "Studio"],
    aspectRatio: "4:3",
    thumbnailUrl:
      "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=800&auto=format&fit=crop",
    url: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=1920&auto=format&fit=crop",
    isGenerated: false,
    isSelected: false,
    settings: {
      quality: "High",
      fps: 60,
      bitrate: "8 Mbps",
      codec: "H.264",
    },
  },
  {
    id: "7",
    title: "Product Showcase",
    resolution: "3840x2160",
    format: "JPEG",
    style: ["Commercial", "Product", "Clean"],
    aspectRatio: "16:9",
    thumbnailUrl:
      "https://images.unsplash.com/photo-1491933382434-500287f9b54b?w=800&auto=format&fit=crop",
    url: "https://images.unsplash.com/photo-1491933382434-500287f9b54b?w=1920&auto=format&fit=crop",
    isGenerated: true,
    isSelected: false,
    settings: {
      quality: "Ultra",
      fps: 60,
      bitrate: "12 Mbps",
      codec: "H.264",
    },
  },
  {
    id: "8",
    title: "Retro Vibes",
    resolution: "2560x1440",
    format: "JPEG",
    style: ["Retro", "Vintage", "Artistic"],
    aspectRatio: "4:3",
    thumbnailUrl:
      "https://images.unsplash.com/photo-1495121605193-b116b5b9c5fe?w=800&auto=format&fit=crop",
    url: "https://images.unsplash.com/photo-1495121605193-b116b5b9c5fe?w=1920&auto=format&fit=crop",
    isGenerated: false,
    isSelected: false,
    settings: {
      quality: "High",
      fps: 60,
      bitrate: "8 Mbps",
      codec: "H.264",
    },
  },
  {
    id: "9",
    title: "Futuristic Tech",
    resolution: "3840x2160",
    format: "JPEG",
    style: ["Futuristic", "Technology", "Modern"],
    aspectRatio: "16:9",
    thumbnailUrl:
      "https://images.unsplash.com/photo-1535223289827-42f1e9919769?w=800&auto=format&fit=crop",
    url: "https://images.unsplash.com/photo-1535223289827-42f1e9919769?w=1920&auto=format&fit=crop",
    isGenerated: true,
    isSelected: false,
    settings: {
      quality: "Ultra",
      fps: 60,
      bitrate: "12 Mbps",
      codec: "H.264",
    },
  },
  {
    id: "10",
    title: "Abstract Art",
    resolution: "2560x1440",
    format: "JPEG",
    style: ["Abstract", "Artistic", "Creative"],
    aspectRatio: "1:1",
    thumbnailUrl:
      "https://images.unsplash.com/photo-1541701494587-cb58502866ab?w=800&auto=format&fit=crop",
    url: "https://images.unsplash.com/photo-1541701494587-cb58502866ab?w=1920&auto=format&fit=crop",
    isGenerated: true,
    isSelected: false,
    settings: {
      quality: "High",
      fps: 60,
      bitrate: "8 Mbps",
      codec: "H.264",
    },
  },
];

interface PhotoGridProps {
  photos: Photo[];
  isSelectionMode: boolean;
  selectedPhotos: string[];
  togglePhotoSelection: (photoId: string) => void;
  handleEdit: (photo: Photo) => void;
  columnCount: 3 | 6;
}

function PhotoGrid({
  photos,
  isSelectionMode,
  selectedPhotos,
  togglePhotoSelection,
  handleEdit,
  columnCount,
}: PhotoGridProps) {
  if (photos.length === 0) {
    return (
      <div className="p-8 bg-gray-50 dark:bg-gray-800 rounded-lg text-center border border-gray-200 dark:border-gray-700">
        <p className="text-gray-500 dark:text-gray-400">
          No images found in this category.
        </p>
      </div>
    );
  }

  const gridClass =
    columnCount === 3
      ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
      : "grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4";

  return (
    <div className={gridClass}>
      {photos.map((photo) => (
        <div
          key={photo.id}
          className={`group relative bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden flex flex-col h-full ${
            isSelectionMode ? "cursor-pointer" : ""
          }`}
          onClick={() => isSelectionMode && togglePhotoSelection(photo.id)}
        >
          <div className="relative">
            <div className="aspect-w-16 aspect-h-9 w-full">
              <img
                src={photo.thumbnailUrl}
                alt={photo.title}
                className="object-cover w-full h-full"
              />
            </div>

            {/* Badge for generated/uploaded */}
            {photo.isGenerated ? (
              <div className="absolute top-2 left-2">
                <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900/50 dark:text-indigo-300">
                  <Sparkles className="w-3 h-3 mr-1" />
                  AI Generated
                </span>
              </div>
            ) : (
              <div className="absolute top-2 left-2">
                <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300">
                  <Upload className="w-3 h-3 mr-1" />
                  Uploaded
                </span>
              </div>
            )}

            {/* Selection indicator */}
            {isSelectionMode && (
              <div className="absolute top-2 right-2">
                <div
                  className={`w-6 h-6 rounded-full border-2 ${
                    selectedPhotos.includes(photo.id)
                      ? "bg-indigo-600 border-indigo-600"
                      : "border-gray-300 dark:border-gray-600"
                  }`}
                >
                  {selectedPhotos.includes(photo.id) && (
                    <CheckCircle2 className="w-5 h-5 text-white" />
                  )}
                </div>
              </div>
            )}
          </div>

          <div className="p-4 flex flex-col flex-grow">
            {/* Spacer to push everything to bottom */}
            <div className="flex-grow"></div>

            {/* Resolution and format info */}
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
              {photo.resolution} • {photo.format}
            </p>

            {/* Title with view button inline */}
            <div className="flex justify-between items-center mb-2">
              <h3
                className={`${columnCount === 6 ? "text-base" : "text-lg"} font-medium text-gray-900 dark:text-white line-clamp-1`}
              >
                {photo.title}
              </h3>

              {!isSelectionMode && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    window.open(photo.url, "_blank");
                  }}
                  className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 flex-shrink-0 ml-2"
                  title="View full image"
                >
                  <ImageIcon className="w-5 h-5" />
                </button>
              )}
            </div>

            {/* Tags */}
            <div className="flex flex-wrap gap-2">
              {photo.style.map((tag) => (
                <span
                  key={tag}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

interface SectionProps {
  title: string;
  icon: React.ComponentType<any>;
  count: number;
  defaultExpanded?: boolean;
  children: React.ReactNode;
}

function Section({
  title,
  icon: Icon,
  count,
  defaultExpanded = true,
  children,
}: SectionProps) {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  return (
    <div className="mb-8">
      <div
        className="flex items-center justify-between mb-4 cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center space-x-2">
          <Icon className="w-5 h-5 text-gray-700 dark:text-gray-300" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {title}
          </h2>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            ({count})
          </span>
        </div>
        <button className="text-gray-500 dark:text-gray-400">
          {isExpanded ? (
            <ChevronUp className="w-5 h-5" />
          ) : (
            <ChevronDown className="w-5 h-5" />
          )}
        </button>
      </div>

      {isExpanded && children}
    </div>
  );
}

interface UploadModalProps {
  onClose: () => void;
  onUpload: (photos: Omit<Photo, "id">[]) => void;
}

const MAX_UPLOADS = 10; // Maximum number of images allowed to upload at once

function UploadModal({ onClose, onUpload }: UploadModalProps) {
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [previews, setPreviews] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [titles, setTitles] = useState<string[]>([]);
  const [tags, setTags] = useState<string[][]>([]);
  const [currentTag, setCurrentTag] = useState("");
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file selection
  const handleFileChange = useCallback(
    (files: FileList | null) => {
      if (!files) return;

      // Check if adding these files would exceed the maximum
      if (uploadedFiles.length + files.length > MAX_UPLOADS) {
        alert(`You can only upload up to ${MAX_UPLOADS} images at a time.`);
        return;
      }

      const validFiles: File[] = [];
      const newPreviews: string[] = [];
      const newTitles: string[] = [];
      const newTags: string[][] = [];

      // Process each file
      Array.from(files).forEach((file) => {
        // Validate file is an image
        if (!file.type.startsWith("image/")) return;

        // Create preview URL
        const previewUrl = URL.createObjectURL(file);

        validFiles.push(file);
        newPreviews.push(previewUrl);
        newTitles.push(file.name.split(".")[0]); // Use filename as initial title
        newTags.push([]); // Empty tags array for this file
      });

      setUploadedFiles((prev) => [...prev, ...validFiles]);
      setPreviews((prev) => [...prev, ...newPreviews]);
      setTitles((prev) => [...prev, ...newTitles]);
      setTags((prev) => [...prev, ...newTags]);
    },
    [uploadedFiles.length],
  );

  // Handle drag events
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  // Handle drop event
  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setDragActive(false);

      if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
        handleFileChange(e.dataTransfer.files);
      }
    },
    [handleFileChange],
  );

  // Handle removing a file
  const removeFile = (index: number) => {
    const newFiles = [...uploadedFiles];
    const newPreviews = [...previews];
    const newTitles = [...titles];
    const newTags = [...tags];

    // Release URL object to prevent memory leaks
    URL.revokeObjectURL(previews[index]);

    newFiles.splice(index, 1);
    newPreviews.splice(index, 1);
    newTitles.splice(index, 1);
    newTags.splice(index, 1);

    setUploadedFiles(newFiles);
    setPreviews(newPreviews);
    setTitles(newTitles);
    setTags(newTags);
  };

  // Handle title change
  const handleTitleChange = (index: number, newTitle: string) => {
    const newTitles = [...titles];
    newTitles[index] = newTitle;
    setTitles(newTitles);
  };

  // Handle adding a tag
  const handleAddTag = (index: number) => {
    if (!currentTag.trim()) return;

    const newTags = [...tags];
    if (!newTags[index].includes(currentTag)) {
      newTags[index] = [...newTags[index], currentTag];
      setTags(newTags);
    }
    setCurrentTag("");
  };

  // Handle removing a tag
  const handleRemoveTag = (fileIndex: number, tagIndex: number) => {
    const newTags = [...tags];
    newTags[fileIndex].splice(tagIndex, 1);
    setTags(newTags);
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (uploadedFiles.length === 0) return;

    setIsUploading(true);

    try {
      // Create photo objects from uploaded files
      const photoObjects: Omit<Photo, "id">[] = uploadedFiles.map(
        (file, index) => {
          // Get image dimensions
          const img = new Image();
          img.src = previews[index];
          const aspectRatio = `${img.width}:${img.height}`;
          const resolution = `${img.width}x${img.height}`;

          // Make sure we have at least an empty array for style if no tags were added
          const photoTags =
            tags[index] && tags[index].length > 0 ? tags[index] : [];

          return {
            title: titles[index],
            thumbnailUrl: previews[index],
            url: previews[index],
            resolution: resolution,
            format: file.type.split("/")[1].toUpperCase(),
            style: photoTags,
            aspectRatio: aspectRatio,
            isGenerated: false,
            isSelected: false,
            settings: {
              quality: "High",
              fps: 60,
              bitrate: "8 Mbps",
              codec: "H.264",
            },
          };
        },
      );

      // In a real app, you would upload the files to a server here
      // For now, we'll just pass the photo objects to the parent component
      onUpload(photoObjects);
    } catch (error) {
      console.error("Error uploading files:", error);
    } finally {
      setIsUploading(false);
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50 flex items-center justify-center">
      <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-lg w-full max-w-3xl mx-4 max-h-[80vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
            <Upload className="w-5 h-5 mr-2" />
            Upload Photos
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Body */}
        <div className="p-6 overflow-y-auto flex-grow">
          {/* Drag and drop area */}
          {uploadedFiles.length < MAX_UPLOADS && (
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center mb-6 ${
                dragActive
                  ? "border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20"
                  : "border-gray-300 dark:border-gray-700 hover:border-indigo-500 hover:bg-indigo-50 dark:hover:bg-indigo-900/10"
              }`}
              onDragEnter={handleDrag}
              onDragOver={handleDrag}
              onDragLeave={handleDrag}
              onDrop={handleDrop}
            >
              <FileImage className="w-12 h-12 mx-auto mb-4 text-gray-400 dark:text-gray-500" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Drag and drop your images here
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-4">
                or click to browse your files
              </p>
              <button
                onClick={() => fileInputRef.current?.click()}
                className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-500 dark:hover:bg-indigo-600 text-white rounded-lg"
              >
                Select Images
              </button>
              <input
                type="file"
                ref={fileInputRef}
                onChange={(e) => handleFileChange(e.target.files)}
                multiple
                accept="image/*"
                className="hidden"
              />
              <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                Maximum {MAX_UPLOADS} images • Accepted formats: JPG, PNG, GIF,
                WebP
              </p>
            </div>
          )}

          {/* Preview area */}
          {uploadedFiles.length > 0 && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Selected Images ({uploadedFiles.length}/{MAX_UPLOADS})
              </h3>

              {previews.map((preview, index) => (
                <div
                  key={index}
                  className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden"
                >
                  {/* Change from flex-col to flex-col-reverse to put image details below the image on mobile */}
                  <div className="flex flex-col-reverse md:flex-row">
                    {/* Image preview container - make it full width on mobile */}
                    <div className="w-full md:w-2/5 h-64 relative flex items-center justify-center bg-gray-100 dark:bg-gray-800 p-6">
                      <img
                        src={preview}
                        alt={`Preview ${index + 1}`}
                        className="max-w-full max-h-full object-contain"
                      />
                      {/* File type badge at top - add more distance from edge */}
                      <div className="absolute top-4 left-4">
                        <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300">
                          <FileImage className="w-3 h-3 mr-1" />
                          {uploadedFiles[index].type
                            .split("/")[1]
                            .toUpperCase()}
                        </span>
                      </div>
                      <button
                        onClick={() => removeFile(index)}
                        className="absolute top-4 right-4 bg-red-100 dark:bg-red-900/50 p-1 rounded-full text-red-600 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-800/50"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>

                    {/* Image details */}
                    <div className="p-6 md:w-3/5 flex flex-col">
                      {/* Spacer to push everything to bottom */}
                      <div className="flex-grow"></div>

                      {/* File size */}
                      <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                        {uploadedFiles[index].size
                          ? Math.round(uploadedFiles[index].size / 1024) + " KB"
                          : "Unknown size"}
                      </p>

                      <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Title
                        </label>
                        <input
                          type="text"
                          value={titles[index]}
                          onChange={(e) =>
                            handleTitleChange(index, e.target.value)
                          }
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          placeholder="Enter a title for this image"
                        />
                      </div>

                      <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Tags
                        </label>
                        <div className="flex flex-wrap gap-2 mb-2">
                          {tags[index].map((tag, tagIndex) => (
                            <span
                              key={tagIndex}
                              className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400"
                            >
                              {tag}
                              <button
                                onClick={() => handleRemoveTag(index, tagIndex)}
                                className="ml-1 text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300"
                              >
                                <X className="w-3 h-3" />
                              </button>
                            </span>
                          ))}
                        </div>
                        <div className="flex">
                          <input
                            type="text"
                            value={currentTag}
                            onChange={(e) => setCurrentTag(e.target.value)}
                            className="flex-grow px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-l-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                            placeholder="Add a tag"
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                e.preventDefault();
                                handleAddTag(index);
                              }
                            }}
                          />
                          <button
                            onClick={() => handleAddTag(index)}
                            className="px-3 py-2 bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-500 dark:hover:bg-indigo-600 text-white rounded-r-md"
                          >
                            Add
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Empty state */}
          {uploadedFiles.length === 0 && (
            <div className="text-center py-4">
              <p className="text-gray-500 dark:text-gray-400">
                No images selected yet.
              </p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg mr-2 hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={uploadedFiles.length === 0 || isUploading}
            className={`px-4 py-2 rounded-lg flex items-center ${
              uploadedFiles.length === 0 || isUploading
                ? "bg-indigo-400 dark:bg-indigo-500/50 cursor-not-allowed"
                : "bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-500 dark:hover:bg-indigo-600"
            } text-white`}
          >
            {isUploading ? (
              <>
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                <span>Uploading...</span>
              </>
            ) : (
              <>
                <Upload className="w-4 h-4 mr-2" />
                <span>
                  Upload {uploadedFiles.length}{" "}
                  {uploadedFiles.length === 1 ? "Image" : "Images"}
                </span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}

// Cleanup Modal Component
function CleanupModal({
  isOpen,
  onClose,
  photos,
  onDeletePhotos
}: {
  isOpen: boolean;
  onClose: () => void;
  photos: Photo[];
  onDeletePhotos: (photoIds: string[]) => void;
}) {
  const [selectedCleanupPhotos, setSelectedCleanupPhotos] = useState<string[]>([]);

  // Sort photos by date (oldest first) - using a simple approach since we don't have creation dates
  const sortedPhotos = [...photos].sort((a, b) => a.id.localeCompare(b.id));

  const handleTogglePhoto = (photoId: string) => {
    setSelectedCleanupPhotos(prev =>
      prev.includes(photoId)
        ? prev.filter(id => id !== photoId)
        : [...prev, photoId]
    );
  };

  const handleSelectOldest = (count: number) => {
    const oldestPhotos = sortedPhotos.slice(0, count).map(photo => photo.id);
    setSelectedCleanupPhotos(oldestPhotos);
  };

  const handleDeleteSelected = () => {
    if (selectedCleanupPhotos.length === 0) {
      alert('Please select photos to delete.');
      return;
    }

    if (window.confirm(`Are you sure you want to delete ${selectedCleanupPhotos.length} selected photo${selectedCleanupPhotos.length > 1 ? 's' : ''}?`)) {
      onDeletePhotos(selectedCleanupPhotos);
      setSelectedCleanupPhotos([]);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-6xl w-full max-h-[80vh] overflow-hidden">
        <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">Clean Up Photos</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              You have {photos.length} photos. Select photos to delete to free up space.
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-6">
          <div className="flex flex-wrap gap-2 mb-4">
            <button
              onClick={() => handleSelectOldest(5)}
              className="px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded text-sm"
            >
              Select 5 Oldest
            </button>
            <button
              onClick={() => handleSelectOldest(10)}
              className="px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded text-sm"
            >
              Select 10 Oldest
            </button>
            <button
              onClick={() => handleSelectOldest(20)}
              className="px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded text-sm"
            >
              Select 20 Oldest
            </button>
            <button
              onClick={() => setSelectedCleanupPhotos([])}
              className="px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded text-sm"
            >
              Clear Selection
            </button>
          </div>

          <div className="max-h-96 overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-lg">
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-3 p-4">
              {sortedPhotos.map(photo => (
                <div
                  key={photo.id}
                  className={`border rounded-lg overflow-hidden cursor-pointer transition-colors ${
                    selectedCleanupPhotos.includes(photo.id)
                      ? 'border-red-300 bg-red-50 dark:border-red-500 dark:bg-red-900/20'
                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                  }`}
                  onClick={() => handleTogglePhoto(photo.id)}
                >
                  <div className="relative">
                    <img
                      src={photo.thumbnailUrl}
                      alt={photo.title}
                      className="w-full h-24 object-cover"
                    />
                    <div className="absolute top-1 right-1">
                      <input
                        type="checkbox"
                        checked={selectedCleanupPhotos.includes(photo.id)}
                        onChange={() => handleTogglePhoto(photo.id)}
                        className="rounded border-gray-300 text-red-600"
                      />
                    </div>
                    {photo.isGenerated && (
                      <div className="absolute top-1 left-1">
                        <span className="inline-flex items-center px-1 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900/50 dark:text-indigo-300">
                          <Sparkles className="w-2 h-2 mr-0.5" />
                          AI
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="p-2">
                    <p className="text-xs text-gray-800 dark:text-gray-200 line-clamp-2 font-medium">
                      {photo.title}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {photo.resolution}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="px-6 py-4 bg-gray-50 dark:bg-gray-800/50 border-t border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {selectedCleanupPhotos.length} photo{selectedCleanupPhotos.length !== 1 ? 's' : ''} selected for deletion
          </div>
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              Cancel
            </button>
            <button
              onClick={handleDeleteSelected}
              disabled={selectedCleanupPhotos.length === 0}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white rounded-lg disabled:cursor-not-allowed"
            >
              Delete Selected
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export function PhotoPage() {
  const [photos, setPhotos] = useState<Photo[]>(samplePhotos);
  const [selectedPhotos, setSelectedPhotos] = useState<string[]>([]);
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [editingPhoto, setEditingPhoto] = useState<Photo | null>(null);
  const [columnCount, setColumnCount] = useState<3 | 6>(6); // Default to 6 columns
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [showCleanupModal, setShowCleanupModal] = useState(false);
  const { tokens } = useTokens();
  const navigate = useNavigate();

  // Separate photos by type
  const uploadedPhotos = photos.filter((photo) => !photo.isGenerated);
  const generatedPhotos = photos.filter((photo) => photo.isGenerated);

  const handleEdit = (photo: Photo) => {
    if (!isSelectionMode) {
      setEditingPhoto({ ...photo, isNew: false });
    }
  };

  const handleCreateNew = () => {
    // Navigate to the Image Studio instead of opening a modal
    navigate("/image-studio");
  };

  const handleUploadNew = () => {
    setIsUploadModalOpen(true);
  };

  const handlePhotoUpload = (newPhotos: Omit<Photo, "id">[]) => {
    // Assign IDs to new photos and add them to the state
    const photosWithIds = newPhotos.map((photo) => ({
      ...photo,
      id: Math.random().toString(36).substr(2, 9),
    }));

    setPhotos((prev) => [...prev, ...photosWithIds]);
  };

  const handleSave = (photo: Photo) => {
    if ("isNew" in photo) {
      setPhotos((prev) => [...prev, { ...photo, isNew: undefined }]);
    } else {
      setPhotos((prev) => prev.map((p) => (p.id === photo.id ? photo : p)));
    }
    setEditingPhoto(null);
  };

  const togglePhotoSelection = (photoId: string) => {
    if (isSelectionMode) {
      setSelectedPhotos((prev) =>
        prev.includes(photoId)
          ? prev.filter((id) => id !== photoId)
          : [...prev, photoId],
      );
    }
  };

  const toggleSelectionMode = () => {
    setIsSelectionMode((prev) => !prev);
    setSelectedPhotos([]);
  };

  const handleSelectAll = () => {
    setSelectedPhotos(photos.map((photo) => photo.id));
  };

  const handleDeselectAll = () => {
    setSelectedPhotos([]);
  };

  const handleDeleteSelected = () => {
    const selectedCount = selectedPhotos.length;
    if (selectedCount === 0) {
      alert('No photos selected for deletion.');
      return;
    }

    if (window.confirm(`Are you sure you want to delete ${selectedCount} selected photo${selectedCount > 1 ? 's' : ''}? This action cannot be undone.`)) {
      setPhotos((prev) => prev.filter((photo) => !selectedPhotos.includes(photo.id)));
      setSelectedPhotos([]);
    }
  };

  const handleCleanupPhotos = () => {
    setShowCleanupModal(true);
  };

  const handleCleanupDeletePhotos = (photoIds: string[]) => {
    setPhotos(prev => prev.filter(photo => !photoIds.includes(photo.id)));
  };

  const handleDelete = () => {
    if (selectedPhotos.length > 0) {
      setPhotos((prev) =>
        prev.filter((photo) => !selectedPhotos.includes(photo.id)),
      );
      setSelectedPhotos([]);
      setIsSelectionMode(false);
    }
  };

  const handleDeleteSingle = (photoId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setPhotos((prev) => prev.filter((photo) => photo.id !== photoId));
  };

  const toggleColumnCount = () => {
    setColumnCount((prev) => (prev === 3 ? 6 : 3));
  };

  const actions = (
    <div className="flex flex-wrap items-center gap-4">
      <TokenBalance tokens={tokens} />
      <div className="flex flex-wrap items-center gap-2">
        <button
          onClick={handleUploadNew}
          className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
        >
          <Upload className="w-4 h-4" />
          <span>Upload</span>
        </button>
        <button
          onClick={handleCreateNew}
          className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
        >
          <Sparkles className="w-4 h-4" />
          <span>Image Studio</span>
        </button>
      </div>
    </div>
  );

  // Count selected photos
  const selectedCount = selectedPhotos.length;

  return (
    <PageLayout icon={ImageIcon} title="My Portfolio" actions={actions}>
      <div className="space-y-6">
        {/* Photo count and cleanup button */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden">
          <div className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  <span className="font-medium">{photos.length}</span> photos in portfolio
                </div>
              </div>
              <button
                onClick={handleCleanupPhotos}
                className="px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg text-sm font-medium"
              >
                Manage Photos
              </button>
            </div>
          </div>
        </div>

        {/* Bulk actions bar */}
        {selectedCount > 0 && (
          <div className="bg-indigo-50 dark:bg-indigo-900/30 border border-indigo-200 dark:border-indigo-700 rounded-xl p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <CheckCircle2 className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
                  <span className="text-sm font-medium text-indigo-800 dark:text-indigo-200">
                    {selectedCount} photo{selectedCount > 1 ? 's' : ''} selected
                  </span>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={handleDeselectAll}
                  className="px-3 py-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg text-sm hover:bg-gray-50 dark:hover:bg-gray-600"
                >
                  Deselect All
                </button>
                <button
                  onClick={handleDeleteSelected}
                  className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm flex items-center space-x-1"
                >
                  <Trash className="w-4 h-4" />
                  <span>Delete Selected</span>
                </button>
              </div>
            </div>
          </div>
        )}

        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <button
              onClick={toggleSelectionMode}
              className="text-sm text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
            >
              {isSelectionMode ? "Cancel Selection" : "Select Photos"}
            </button>
            {isSelectionMode && (
              <div className="flex items-center space-x-2">
                <button
                  onClick={handleSelectAll}
                  className="px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg text-sm flex items-center space-x-1"
                >
                  <CheckSquare className="w-4 h-4" />
                  <span>Select All</span>
                </button>
                <button
                  onClick={handleDeselectAll}
                  className="px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg text-sm flex items-center space-x-1"
                >
                  <Square className="w-4 h-4" />
                  <span>Deselect All</span>
                </button>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600 dark:text-gray-300">
              Grid Size:
            </span>
            <button
              onClick={toggleColumnCount}
              className={`px-3 py-1 text-sm rounded-lg ${
                columnCount === 3
                  ? "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/50 dark:text-indigo-300"
                  : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
              }`}
            >
              3×
            </button>
            <button
              onClick={toggleColumnCount}
              className={`px-3 py-1 text-sm rounded-lg ${
                columnCount === 6
                  ? "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/50 dark:text-indigo-300"
                  : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
              }`}
            >
              6×
            </button>
          </div>
        </div>

        <Section
          title="Uploaded Images"
          icon={Upload}
          count={uploadedPhotos.length}
          defaultExpanded={true}
        >
          <PhotoGrid
            photos={uploadedPhotos}
            isSelectionMode={isSelectionMode}
            selectedPhotos={selectedPhotos}
            togglePhotoSelection={togglePhotoSelection}
            handleEdit={handleEdit}
            columnCount={columnCount}
          />
        </Section>

        <Section
          title="AI-Generated Images"
          icon={Sparkles}
          count={generatedPhotos.length}
          defaultExpanded={true}
        >
          <PhotoGrid
            photos={generatedPhotos}
            isSelectionMode={isSelectionMode}
            selectedPhotos={selectedPhotos}
            togglePhotoSelection={togglePhotoSelection}
            handleEdit={handleEdit}
            columnCount={columnCount}
          />
        </Section>
      </div>

      {editingPhoto && (
        <PhotoEditor
          photo={editingPhoto}
          onSave={handleSave}
          onClose={() => setEditingPhoto(null)}
        />
      )}

      {isUploadModalOpen && (
        <UploadModal
          onClose={() => setIsUploadModalOpen(false)}
          onUpload={handlePhotoUpload}
        />
      )}

      <CleanupModal
        isOpen={showCleanupModal}
        onClose={() => setShowCleanupModal(false)}
        photos={photos}
        onDeletePhotos={handleCleanupDeletePhotos}
      />
    </PageLayout>
  );
}
