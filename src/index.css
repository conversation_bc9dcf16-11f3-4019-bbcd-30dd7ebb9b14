@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --tw-text-opacity-60: rgba(79, 70, 229, 0.6);
}

body {
  margin: 0;
  min-height: 100vh;
}

#root {
  min-height: 100vh;
}

/* Custom Scrollbar Styles */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-gray-200 {
  scrollbar-color: #e5e7eb transparent;
}

.dark .scrollbar-thumb-gray-700 {
  scrollbar-color: #374151 transparent;
}

.scrollbar-track-transparent {
  scrollbar-track-color: transparent;
}

/* For Webkit browsers */
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thumb-gray-200::-webkit-scrollbar-thumb {
  background-color: #e5e7eb;
  border-radius: 3px;
}

.dark .scrollbar-thumb-gray-700::-webkit-scrollbar-thumb {
  background-color: #374151;
}

/* Character Lab Specific Styles */
.character-lab-modal {
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.character-lab-modal-content {
  flex: 1;
  overflow-y: auto;
}

/* Animation Keyframes */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Add different delays for floating elements */
.delay-300 {
  animation-delay: 300ms;
}

.delay-500 {
  animation-delay: 500ms;
}

.delay-700 {
  animation-delay: 700ms;
}

/* Line clamp utilities */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-6 {
  display: -webkit-box;
  -webkit-line-clamp: 6;
  -webkit-box-orient: vertical;
  overflow: hidden;
}